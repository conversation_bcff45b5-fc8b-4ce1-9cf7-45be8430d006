# 外网访问代理
默认情况下，由于网络限制，我们部署的应用是无法直接访问外网的。如果有访问外网需求，可按以下的【接入流程】配置应用

### 接入流程 (两个步骤)
- 第一步：发审批给 @吴志辉，备注应用信息及需要访问的外网（host）地址。这些地址会添加到代理白名单列表中。模板：
  ```
  #应用外网访问申请(代理模式)#
  
  应用名：  fs-app-xxx
  地址： www.google.com, www.xyz.com, *.aliyuncs.com
  备注：<简单描述下访问需求>
  ```

- 第二步：业务代码中给访问外网的HttpClient配置代理地址，参见【代理地址】 。
> 对于http访问，请使用我们的http组件（http-spring-support）,它已集成对代理地址的配置。具体使用间【参考信息】


### 参考信息

- **代理地址**

|**环境**|**地址**|
|---|---|
|线下 | **************:9999 |
|线上 | ************:9999 |
|ceshi114| ************:9999 |

- **对于http访问，强烈推荐使用我们自己的http组件（http-spring-support）, 可以在配置中心通过httpProxy来配置代理。**
关于组件的具体配置可以参考：http://git.firstshare.cn/JavaCommon/http-spring-support
这是线下某个应用的Demo配置
```
connectTimeout=3
readTimeout=30
writeTimeout=5
maxRequests=200
maxRequestsPerHost=100
retryOnConnectionFailure=true
maxIdleConnections=5
keepAliveDuration=50
isKeepAlive=true
httpProxy=**************:9999
```

- **对于部分访问地址（如：内网ip,内部域名，k8s下的服务名等）请不要通过代理来访问**
 http-spring-support 对访问地址会做识别，对于上面的这些地址会自动跳过代理直接访问

## Https

制作https证书：

```bash

keytool -genkey -alias tomcat -dname "CN=com.fxiaoke,OU=self,O=self,L=HaiDian,ST=BeiJing,C=CN" -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 36500
# 输入密码

```

