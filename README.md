# 外网访问代理服务 (Egress Proxy Service)

基于Spring Boot和Netty的高性能外网访问代理服务，为内网应用提供安全、可控的外网访问能力。支持HTTP/HTTPS代理和WebSocket隧道两种代理模式。

## 🚀 核心能力

### 1. HTTP/HTTPS 代理模式

- **协议支持**: HTTP/1.1、HTTPS、WebSocket over HTTP
- **访问控制**:
  - 域名白名单（支持泛域名 `*.example.com`）
  - 应用白名单（通过 `X-Peer-Name` 头识别）
  - Basic认证支持
  - 内网地址访问控制
- **性能优化**:
  - 连接池管理
  - 流量限速（读写分离限速）
  - 连接超时控制
  - 自动重连机制
- **监控指标**:
  - 按应用、主机统计流量
  - 连接数监控
  - 错误率统计
  - Prometheus集成

### 2. WebSocket 隧道代理模式（双向代理）

- **双向代理架构**:
  - **隧道服务端**: 接收WebSocket连接，转发到目标服务
  - **隧道客户端**: 建立WebSocket连接，提供本地代理端口
  - **统一服务**: 同一个应用既可作为服务端，也可作为客户端
- **跨云访问**: 支持专属云之间的双向服务互访
- **安全传输**:
  - 基于WSS (WebSocket Secure) 协议
  - 数据包级别加密（可选）
  - 随机密钥生成
  - 端到端安全隧道
- **协议支持**: 任意TCP协议（HTTP、MySQL、Redis、SSH等）
- **流量控制**:
  - 全局流量限速
  - 单连接流量限速
  - 缓冲区大小控制
  - 双向流量统计
- **高可用性**:
  - 自动断线重连
  - 连接健康检查
  - 服务白名单管理
  - 故障转移支持
- **心跳机制**:
  - 双向PING/PONG心跳检测
  - 可配置心跳间隔和超时
  - 自动故障检测和重连
  - 连接状态实时监控

### 3. 运维监控能力

- **实时监控**:
  - 流量统计（发送/接收字节数）
  - 连接状态监控
  - 心跳状态监控
  - 错误日志记录
- **配置管理**:
  - 动态配置刷新（无需重启）
  - 配置中心集成
  - 环境隔离配置
- **日志管理**:
  - 分级日志输出
  - 异步日志处理
  - 日志轮转和压缩
  - 访问日志记录

## 📋 详细配置说明

### WebSocket隧道配置 (`egress.websocket.*`)

| 配置项 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `context-path` | `/FWD` | WebSocket服务路径 | `/tunnel` |
| `allowed-origins` | `*` | 允许的跨域来源 | `https://app.example.com` |
| `global-read-bytes-per-second` | `10485760` | 全局读取限速（字节/秒） | `20971520` |
| `global-write-bytes-per-second` | `10485760` | 全局写入限速（字节/秒） | `20971520` |
| `send-time-limit` | `60000` | 发送超时时间（毫秒） | `30000` |
| `send-buffer-size` | `128KB` | 发送缓冲区大小 | `256KB` |
| `enable-debug` | `false` | 是否启用调试模式 | `true` |
| `allowed-hosts` | `["*"]` | IP白名单（支持正则） | `["192\\.168\\..*","10\\..*"]` |
| `ignore-errors` | `[]` | 忽略错误的服务列表 | `["unstable-service"]` |
| `servers.*` | `{}` | 服务白名单配置 | 见下方示例 |
| `enable-heartbeat` | `true` | 是否启用心跳机制 | `false` |
| `heartbeat-interval` | `30` | 心跳间隔时间（秒） | `60` |
| `heartbeat-timeout` | `10` | 心跳超时时间（秒） | `15` |
| `max-heartbeat-failures` | `3` | 最大心跳失败次数 | `5` |

#### WebSocket隧道配置示例

```properties
# WebSocket服务配置
egress.websocket.context-path=/FWD
egress.websocket.allowed-origins=*

# 全局流量限制 (20MB/s)
egress.websocket.global-read-bytes-per-second=20971520
egress.websocket.global-write-bytes-per-second=20971520

# 缓冲区和超时配置
egress.websocket.send-time-limit=30000
egress.websocket.send-buffer-size=256KB
egress.websocket.enable-debug=false

# 心跳配置
egress.websocket.enable-heartbeat=true
egress.websocket.heartbeat-interval=30
egress.websocket.heartbeat-timeout=10
egress.websocket.max-heartbeat-failures=3

# 访问控制
egress.websocket.allowed-hosts=192\.168\..*,10\..*,172\.16\..*

# 服务白名单 - 配置允许访问的目标服务
egress.websocket.servers.configSync=***********:8080
egress.websocket.servers.dataSync=*************:3306
egress.websocket.servers.cacheProxy=***********:6379

# 忽略特定服务的错误日志
egress.websocket.ignore-errors=unstable-service,legacy-system
```

## 💓 心跳机制详解

### 心跳机制原理

WebSocket隧道代理实现了完整的双向心跳机制，确保连接的可靠性和及时的故障检测：

#### 1. 服务端心跳（ShareWebSocketHandler）

- **主动发送**: 定期向客户端发送PING消息
- **被动响应**: 接收客户端PING消息并回复PONG
- **故障检测**: 监控PONG响应超时，超过阈值自动断开连接
- **连接管理**: 为每个WebSocket连接独立管理心跳状态

#### 2. 客户端心跳（ShareSocketClient）

- **主动发送**: 定期向服务端发送PING消息
- **被动响应**: 接收服务端PING消息并回复PONG
- **故障检测**: 监控PONG响应超时，超过阈值自动重连
- **重连机制**: 心跳失败时触发自动重连逻辑

#### 3. 心跳消息格式

```json
{
  "action": "PING",
  "body": "ping",
  "token": "optional_encryption_token"
}

{
  "action": "PONG",
  "body": "pong",
  "token": "optional_encryption_token"
}
```

### 心跳配置参数

#### 服务端配置

```properties
# 启用心跳机制
egress.websocket.enable-heartbeat=true

# 心跳间隔：每30秒发送一次PING
egress.websocket.heartbeat-interval=30

# 心跳超时：10秒内未收到PONG视为超时
egress.websocket.heartbeat-timeout=10

# 最大失败次数：连续3次失败后断开连接
egress.websocket.max-heartbeat-failures=3
```

#### 客户端配置（系统属性）

```properties
# 客户端心跳间隔（秒）
-Degress.websocket.client.heartbeat.interval=30

# 客户端心跳超时（秒）
-Degress.websocket.client.heartbeat.timeout=10

# 客户端最大失败次数
-Degress.websocket.client.heartbeat.max-failures=3
```

### 心跳监控指标

心跳机制提供以下监控能力：

1. **连接状态监控**: 实时显示每个连接的心跳状态
2. **故障检测日志**: 记录心跳超时和连接断开事件
3. **重连统计**: 统计自动重连次数和成功率
4. **性能指标**: 心跳延迟和丢包率统计

### 故障处理流程

```mermaid
graph TD
    A[发送PING] --> B[等待PONG响应]
    B --> C{收到PONG?}
    C -->|是| D[重置失败计数]
    C -->|否| E[增加失败计数]
    E --> F{达到最大失败次数?}
    F -->|否| G[继续下次心跳]
    F -->|是| H[断开连接]
    H --> I[触发重连机制]
    D --> G
    G --> A
```

## 📋 接入流程

### HTTP代理接入流程

#### 第一步：申请访问权限

发送审批申请，模板如下：

```
#应用外网访问申请(HTTP代理模式)#

应用名：fs-app-xxx
访问地址：www.google.com, api.github.com, *.aliyuncs.com
访问用途：调用第三方API获取数据
预估流量：100MB/天
联系人：张三
```

#### 第二步：配置代理地址

在应用中配置HTTP代理：

**使用http-spring-support组件（推荐）：**

```properties
# 配置中心配置
connectTimeout=10
readTimeout=30
writeTimeout=5
maxRequests=200
maxRequestsPerHost=100
retryOnConnectionFailure=true
maxIdleConnections=5
keepAliveDuration=50
isKeepAlive=true
httpProxy=**************:9999
```

### 环境对应的代理地址

| 环境 | HTTP代理地址 | WebSocket隧道地址 |
|------|-------------|------------------|
| 线下环境 | `**************:9999` | `wss://dev-proxy.company.com/FWD` |
| 测试环境 | `************:9999` | `wss://test-proxy.company.com/FWD` |
| 生产环境 | `************:9999` | `wss://prod-proxy.company.com/FWD` |

## 📚 参考信息

### 相关组件

- **http-spring-support**: HTTP客户端组件，已集成代理配置
  - 仓库地址: <http://git.firstshare.cn/JavaCommon/http-spring-support>
  - 使用文档: [HTTP组件使用指南](http://git.firstshare.cn/JavaCommon/http-spring-support)

### 联系方式

- **HTTP代理问题**: @吴志辉
- **隧道代理问题**: @黎贵昂
- **技术支持**: 基础架构团队

