package com.fxiaoke.egress.handler;

import com.fxiaoke.egress.bean.MessageBean;
import com.fxiaoke.egress.util.ProxyUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.nio.ByteBuffer;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 心跳机制测试
 */
public class HeartbeatTest {

    @Test
    @DisplayName("测试PING消息创建和解析")
    public void testPingMessage() {
        // 创建PING消息
        byte[] pingMessage = ProxyUtil.binaryMessage("PING", "ping", false);
        assertNotNull(pingMessage);
        
        // 解析PING消息
        MessageBean bean = MessageBean.fromByteArray(pingMessage);
        assertEquals("PING", bean.getAction());
        assertEquals("ping", new String(bean.decode()));
    }

    @Test
    @DisplayName("测试PONG消息创建和解析")
    public void testPongMessage() {
        // 创建PONG消息
        byte[] pongMessage = ProxyUtil.binaryMessage("PONG", "pong", false);
        assertNotNull(pongMessage);
        
        // 解析PONG消息
        MessageBean bean = MessageBean.fromByteArray(pongMessage);
        assertEquals("PONG", bean.getAction());
        assertEquals("pong", new String(bean.decode()));
    }

    @Test
    @DisplayName("测试加密的心跳消息")
    public void testEncryptedHeartbeat() {
        // 创建加密的PING消息
        byte[] encryptedPing = ProxyUtil.binaryMessage("PING", "ping", true);
        assertNotNull(encryptedPing);
        
        // 解析加密的PING消息
        MessageBean bean = MessageBean.fromByteArray(encryptedPing);
        assertEquals("PING", bean.getAction());
        assertNotNull(bean.getToken()); // 应该有加密token
        assertEquals("ping", new String(bean.decode()));
    }

    @Test
    @DisplayName("测试ByteBuffer消息解析")
    public void testByteBufferParsing() {
        // 创建消息
        byte[] message = ProxyUtil.binaryMessage("PING", "ping", false);
        ByteBuffer buffer = ByteBuffer.wrap(message);
        
        // 从ByteBuffer解析
        MessageBean bean = MessageBean.fromByteBuffer(buffer);
        assertEquals("PING", bean.getAction());
        assertEquals("ping", new String(bean.decode()));
    }
}
