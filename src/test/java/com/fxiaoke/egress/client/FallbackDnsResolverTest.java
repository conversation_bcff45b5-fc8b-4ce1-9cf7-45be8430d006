package com.fxiaoke.egress.client;

import org.junit.jupiter.api.Test;

import java.net.URI;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

class FallbackDnsResolverTest {

  private final FallbackDnsResolver resolver = new FallbackDnsResolver("***********");

  @Test
  void resolve() {
    assertDoesNotThrow(() -> resolver.resolve(URI.create("wss://address.never.found")));
  }
}