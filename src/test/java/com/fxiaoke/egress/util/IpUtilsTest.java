package com.fxiaoke.egress.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotEquals;

class IpUtilsTest {

  @Test
  void roundRobin() {
    String servers = "***********,***********,***********";
    String[] serverLists = servers.split(",");
    assertNotEquals(IpUtils.roundRobin(serverLists), IpUtils.roundRobin(serverLists));
    assertNotEquals(IpUtils.roundRobin(serverLists), IpUtils.roundRobin(serverLists));
    assertNotEquals(IpUtils.roundRobin(serverLists), IpUtils.roundRobin(serverLists));
  }
}