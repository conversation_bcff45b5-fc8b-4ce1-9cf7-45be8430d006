spring.application.name=egress-proxy-service
spring.config.import=optional:cms:${spring.application.name}
server.servlet.context-path=/egress-proxy-service
#spring.profiles.active=fstest
#process.profile=fstest
logging.pattern.console=%d{HH:mm:ss.SSS} %-5level [%t] %logger{16} %msg%n
management.endpoints.web.exposure.include=health,metrics,prometheus
egress.proxy.bind-port=9999
egress.proxy.backlog=4096
egress.proxy.enable-debug=false
egress.proxy.enable-anonymous-access=true
egress.proxy.enable-any-site=true
egress.proxy.enable-local-site=true
egress.proxy.connect-timeout-in-seconds=10
egress.proxy.read-timeout-in-seconds=300
egress.proxy.allowed-apps=fs-paas-function-service-runtime,fs-paas-function-service-debug,fs-paas-function-service-background-provider,fs-paas-function-service-runtime-provider
egress.proxy.allowed-sites=*.aliyuncs.com,*.oss-cn-beijing.aliyuncs.com,*.oss-cn-shanghai.aliyuncs.com,101.251.214.153,120.77.29.95,180.76.232.101,47.100.89.202,a1.7x24cc.com,api-test.rich-healthcare.com,api.bestsign.cn,api.bestsign.info,api.kdniao.com,api.map.baidu.com,api.mch.weixin.qq.com,api.rich-healthcare.com,api.sendcloud.net,api.weixin.qq.com,appo.test.ik3cloud.com,bcr2.intsig.net,ejia.tbea.com,git.firstshare.cn,gosspublic.alicdn.com,gw.wmcloud.com,iam.myhuaweicloud.com,image.cn-north-4.myhuaweicloud.com,kdtest.kdweibo.cn,mobile.lenztechretail.com,mp.weixin.qq.com,open.intranet.fxiaoke.com,oss-cn-beijing.aliyuncs.com,oss-cn-shanghai.aliyuncs.com,oss.firstshare.cn,oss.foneshare.cn,paas-export-test.oss-cn-beijing.aliyuncs.com,poll.kuaidi100.com,qyapi.weixin.qq.com,restapi.amap.com,saas.elecredit.com,sso.iflytek.com,vod.api.qcloud.com,vod.tencentcloudapi.com,wework.qpic.cn,worldnj-1.oss-cn-shanghai.aliyuncs.com,www.baidu.com,www.dh3t.com,www.fxiaoke.com,wx.qlogo.cn,xt.tbea.com,yunzhijia.com
logging.level.root=info
logging.level.com.github=info
logging.level.org.apache.kafka=WARN
logging.level.com.fxiaoke.egress=DEBUG
# will be replaced by cms center
egress.websocket.ignore-errors=foneshareZabbix10050,foneshareZabbix10051

# at cms center
#spring.cloud.gateway.mvc.routes[0].id=egress-proxy-tunnel
#spring.cloud.gateway.mvc.routes[0].uri=http://localhost:8088
#spring.cloud.gateway.mvc.routes[0].predicates[0]=Path=/tunnel/**
#spring.cloud.gateway.mvc.routes[0].filters[0]=ProxyTunnelTarget

