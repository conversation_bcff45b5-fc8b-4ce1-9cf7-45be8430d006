package com.fxiaoke.egress.bean;

import com.fxiaoke.common.crypto.TEA;
import com.google.common.base.MoreObjects;
import io.protostuff.LinkedBuffer;
import io.protostuff.ProtostuffIOUtil;
import io.protostuff.Schema;
import io.protostuff.Tag;
import io.protostuff.runtime.RuntimeSchema;
import lombok.Data;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Optional;

@Data
public class MessageBean {
  private static final Schema<MessageBean> schema = RuntimeSchema.getSchema(MessageBean.class);

  @Tag(1)
  private String token;
  @Tag(2)
  private String action;
  @Tag(3)
  private byte[] body;

  public MessageBean() {
  }

  public MessageBean(String token, String action, byte[] body) {
    this.token = token;
    this.action = action;
    this.body = body;
  }

  public byte[] toByteArray() {
    LinkedBuffer buffer = LinkedBuffer.allocate(2048);
    try {
      return ProtostuffIOUtil.toByteArray(this, schema, buffer);
    } finally {
      buffer.clear();
    }
  }

  public static MessageBean fromByteArray(byte[] data) {
    MessageBean proxyTarget = new MessageBean();
    ProtostuffIOUtil.mergeFrom(data, proxyTarget, schema);
    return proxyTarget;
  }

  public static MessageBean fromByteArray(byte[] data, int offset, int length) {
    MessageBean proxyTarget = new MessageBean();
    ProtostuffIOUtil.mergeFrom(data, offset, length, proxyTarget, schema);
    return proxyTarget;
  }

  public static MessageBean fromByteBuffer(ByteBuffer buf) {
    byte[] array;
    if (buf.hasArray()) {
      array = buf.array();
      int offset = buf.arrayOffset();
      int length = buf.remaining();
      return fromByteArray(array, offset, length);
    } else {
      array = new byte[buf.remaining()];
      buf.get(array);
      return fromByteArray(array);
    }
  }

  public final byte[] encode(byte[] bytes, int offset, int len) {
    return token == null ? Arrays.copyOfRange(bytes, offset, offset + len) : new TEA(token.getBytes(StandardCharsets.UTF_8)).encode2(bytes, offset, len);
  }

  public final byte[] encode(byte[] bytes) {
    return token == null ? bytes : new TEA(token.getBytes(StandardCharsets.UTF_8)).encode2(bytes);
  }

  public final byte[] encode(String txt) {
    return token == null ? txt.getBytes(StandardCharsets.UTF_8) : new TEA(token.getBytes(StandardCharsets.UTF_8)).encode2(txt.getBytes(StandardCharsets.UTF_8));
  }
  
  public final byte[] decode() {
    return token == null ? body : new TEA(token.getBytes(StandardCharsets.UTF_8)).decode(body);
  }

  @Override
  public String toString() {
    String abbrev = Optional.ofNullable(body).map(b -> "bytes[" + b.length + "]").orElse("");
    return MoreObjects.toStringHelper(this).add("action", action).add("token", token).add("body", abbrev).toString();
  }
}
