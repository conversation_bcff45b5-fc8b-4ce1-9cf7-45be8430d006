package com.fxiaoke.egress.bean;

import io.protostuff.LinkedBuffer;
import io.protostuff.ProtostuffIOUtil;
import io.protostuff.Schema;
import io.protostuff.Tag;
import io.protostuff.runtime.RuntimeSchema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ProxyTarget {
  private static final Schema<ProxyTarget> schema = RuntimeSchema.getSchema(ProxyTarget.class);

  @Tag(1)
  private String name;
  @Tag(2)
  private int connectTimeout;
  @Tag(3)
  private int readTimeout;
  @Tag(4)
  private long readBytesPerSecond;
  @Tag(5)
  private long writeBytesPerSecond;
  @Tag(6)
  private boolean secure;

  public byte[] toByteArray() {
    LinkedBuffer buffer = LinkedBuffer.allocate(512);
    try {
      return ProtostuffIOUtil.toByteArray(this, schema, buffer);
    } finally {
      buffer.clear();
    }
  }

  public static ProxyTarget fromByteArray(byte[] data) {
    ProxyTarget proxyTarget = new ProxyTarget();
    ProtostuffIOUtil.mergeFrom(data, proxyTarget, schema);
    return proxyTarget;
  }

  public static ProxyTarget fromByteArray(byte[] data, int offset, int length) {
    ProxyTarget proxyTarget = new ProxyTarget();
    ProtostuffIOUtil.mergeFrom(data, offset, length, proxyTarget, schema);
    return proxyTarget;
  }
}
