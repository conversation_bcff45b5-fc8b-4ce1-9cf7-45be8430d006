package com.fxiaoke.egress.service;

import com.fxiaoke.egress.config.EgressProxyConfig;
import com.fxiaoke.egress.handler.HttpProxyClientHandler;
import com.fxiaoke.egress.metric.EgressMetric;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.traffic.ChannelTrafficShapingHandler;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.net.SocketAddress;

/**
 * 提供http代理服务，支持配置白名单
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HttpProxyServer {
  private final AclService aclService;
  private final EgressProxyConfig config;
  private final EgressMetric metric;

  private Thread worker;

  public HttpProxyServer(AclService aclService, EgressProxyConfig config, EgressMetric metric) {
    this.aclService = aclService;
    this.config = config;
    this.metric = metric;
  }

  @PostConstruct
  void startServer() {
    worker = new Thread(this::proxyJob);
    worker.setName("http-proxy-main");
    worker.start();
  }

  private void proxyJob() {
    EventLoopGroup bossGroup = new NioEventLoopGroup(1);
    EventLoopGroup workerGroup = new NioEventLoopGroup();
    int bindPort = config.getBindPort();
    try {
      log.info("START proxy server on {}", bindPort);
      ServerBootstrap boot = new ServerBootstrap();
      boot.group(bossGroup, workerGroup)
              .channel(NioServerSocketChannel.class)
              .option(ChannelOption.SO_BACKLOG, config.getBacklog())
              .option(ChannelOption.SO_REUSEADDR, Boolean.TRUE)
              .childOption(ChannelOption.TCP_NODELAY, Boolean.TRUE)
              .childOption(ChannelOption.SO_KEEPALIVE, Boolean.TRUE)
              .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, config.getConnectTimeoutInSeconds() * 1000)
              .childHandler(new ChannelInitializer<SocketChannel>() {
                @Override
                protected void initChannel(@NonNull SocketChannel ch) {
                  ChannelPipeline p = ch.pipeline();
                  p.addLast(new ChannelTrafficShapingHandler(config.getWriteBytesPerSeconds(), config.getReadBytesPerSeconds()));
                  p.addLast(new ReadTimeoutHandler(config.getReadTimeoutInSeconds()));
                  if (config.isEnableDebug()) {
                    p.addLast(new LoggingHandler(LogLevel.INFO));
                  }
                  p.addLast(new HttpProxyClientHandler(aclService, config, metric));
                }
              })
              .bind(getBindAddress())
              .sync()
              .channel()
              .closeFuture()
              .sync();
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    } finally {
      log.info("STOP proxy server on {}", bindPort);
      bossGroup.shutdownGracefully();
      workerGroup.shutdownGracefully();
    }
  }

  private SocketAddress getBindAddress() {
    return config.getBindIp() != null ? new InetSocketAddress(config.getBindIp(), config.getBindPort()) : new InetSocketAddress(config.getBindPort());
  }

  @PreDestroy
  void stopServer() {
    worker.interrupt();
  }
}
