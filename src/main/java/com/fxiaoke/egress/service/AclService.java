package com.fxiaoke.egress.service;

import com.fxiaoke.egress.config.EgressProxyConfig;
import com.fxiaoke.egress.handler.HttpHeaderDetector;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 访问控制类
 * 支持泛域名
 * 比如支持匹配*.baidu.com的所有泛域名，请用*.baidu.com配置
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AclService {
  private final EgressProxyConfig config;
  private DomainSegments root;
  private Set<String> allowedApps;
  private Set<String> basic;
  private boolean needAuth;

  public AclService(EgressProxyConfig config) {
    this.config = config;
    this.init();
  }

  private void init() {
    needAuth = !config.isEnableAnonymousAccess();
    basic = config.getBasic();
    allowedApps = config.getAllowedApps();
    Set<String> sites = config.getAllowedSites();
    log.info("allowed sites: {}", sites);
    root = DomainSegments.parseFromLines(sites);
  }

  @EventListener
  public void handleEnvironmentChange(RefreshScopeRefreshedEvent event) {
    log.info("handle RefreshScopeRefreshedEvent:{}, config:{}", event, config);
    init();
  }

  public boolean isDeny(HttpHeaderDetector header) {
    String auth = header.getBasicAuth();
    // 验证是否需要鉴权
    if (needAuth && (auth == null || !basic.contains(auth))) {
      return true;
    }
    // 如果允许访问任意站点，则不做拦截
    if (config.isEnableAnySite()) {
      return false;
    }
    String app = header.getAppName();
    // 验证是否在APP的白名单中
    if (app != null && allowedApps.contains(app)) {
      return false;
    }
    String host = header.getHost();
    // 验证是否在域名的白名单中
    return !DomainSegments.contains(root, host);
  }

  /**
   * 将多个域名分段翻转保存成树结构
   */
  @Getter
  public static class DomainSegments {
    private final String segment;
    private final Map<String, DomainSegments> preMap;

    public DomainSegments(String segment) {
      this.segment = segment;
      preMap = Maps.newHashMap();
    }

    /**
     * 判断一个域名树中是否包含某个域名
     */
    public static boolean contains(DomainSegments root, String domain) {
      if (root == null) {
        return false;
      }
      List<String> segments = Splitter.on('.').trimResults().splitToList(domain);
      DomainSegments it = root;
      for (int i = segments.size() - 1; i >= 0; i--) {
        String segment = segments.get(i);
        if (it.preMap.containsKey(segment)) {
          it = it.preMap.get(segment);
        } else {
          return it.preMap.containsKey("*");
        }
      }
      return true;
    }

    /**
     * 将多个域名分段翻转保存成树结构
     */
    public static DomainSegments parseFromLines(Collection<String> lines) {
      DomainSegments root = new DomainSegments("root");
      if (lines == null || lines.isEmpty()) {
        return root;
      }
      for (String line : lines) {
        if (line.trim().startsWith("#")) {
          continue;
        }
        List<String> segments = Splitter.on('.').trimResults().splitToList(line);
        DomainSegments it = root;
        for (int i = segments.size() - 1; i >= 0; i--) {
          String segment = segments.get(i);
          it = it.preMap.computeIfAbsent(segment, DomainSegments::new);
        }
      }
      return root;
    }
  }
}
