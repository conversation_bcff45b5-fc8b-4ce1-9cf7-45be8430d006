package com.fxiaoke.egress.metric;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Component;

@Component
public class EgressMetric {
  private final MeterRegistry registry;

  public EgressMetric(MeterRegistry registry) {
    this.registry = registry;
  }

  public Counter counter(String name, String... tags) {
    return registry.counter(name, tags);
  }

  public void increment(String name, double amount, String... tags) {
    registry.counter(name, tags).increment(amount);
  }
}
