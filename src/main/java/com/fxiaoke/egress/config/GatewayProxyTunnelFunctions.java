package com.fxiaoke.egress.config;

import org.springframework.cloud.gateway.server.mvc.common.Shortcut;
import org.springframework.cloud.gateway.server.mvc.filter.SimpleFilterSupplier;
import org.springframework.web.servlet.function.HandlerFilterFunction;
import org.springframework.web.servlet.function.ServerResponse;

public interface GatewayProxyTunnelFunctions {

  @Shortcut
  static HandlerFilterFunction<ServerResponse, ServerResponse> proxyTunnelTarget() {
    return HandlerFilterFunction.ofRequestProcessor(GatewayProxyTunnelFilter.proxyTunnelTarget());
  }

  class FilterSupplier extends SimpleFilterSupplier {

    public FilterSupplier() {
      super(GatewayProxyTunnelFunctions.class);
    }

  }

}
