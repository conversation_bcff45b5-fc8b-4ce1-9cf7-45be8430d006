package com.fxiaoke.egress.config;

import com.fxiaoke.egress.bean.ProxyTarget;
import com.fxiaoke.egress.handler.ForwardServerHandler;
import com.fxiaoke.egress.handler.ShareWebSocketHandler;
import com.fxiaoke.egress.handler.WebSocketInterceptor;
import com.fxiaoke.egress.metric.EgressMetric;
import com.fxiaoke.egress.util.ProxyUtil;
import com.google.common.net.HostAndPort;
import io.micrometer.core.instrument.Counter;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.traffic.ChannelTrafficShapingHandler;
import io.netty.handler.traffic.GlobalTrafficShapingHandler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;

import java.io.IOException;
import java.util.function.BiFunction;
import java.util.function.UnaryOperator;

@Slf4j
@Component
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
  private final ShareSocketConfig config;
  private final EgressMetric metric;
  private final EventLoopGroup bossGroup = new NioEventLoopGroup();
  private final GlobalTrafficShapingHandler globalTrafficShapingHandler;

  public WebSocketConfig(ShareSocketConfig config, EgressMetric metric) {
    this.config = config;
    this.metric = metric;
    this.globalTrafficShapingHandler = new GlobalTrafficShapingHandler(bossGroup, config.getGlobalWriteBytesPerSecond(), config.getGlobalReadBytesPerSecond());
  }

  @EventListener
  public void handleEnvironmentChange(EnvironmentChangeEvent event) {
    log.info("handle EnvironmentChangeEvent:{}, config:{}", event, config);
    globalTrafficShapingHandler.setWriteLimit(config.getGlobalWriteBytesPerSecond());
    globalTrafficShapingHandler.setReadLimit(config.getGlobalReadBytesPerSecond());
  }

  @Override
  public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
    UnaryOperator<WebSocketSession> operator = s -> new ConcurrentWebSocketSessionDecorator(s, config.getSendTimeLimit(), (int) config.getSendBufferSize().toBytes());
    BiFunction<ProxyTarget, WebSocketSession, Channel> connector = this::connectTarget;
    registry.addHandler(new ShareWebSocketHandler(operator, connector), config.getContextPath())
            .setAllowedOrigins(config.getAllowedOrigins())
            .addInterceptors(new WebSocketInterceptor(config));
  }

  @SneakyThrows(IOException.class)
  private Channel connectTarget(ProxyTarget dst, WebSocketSession wss) {
    String name = dst.getName();
    String target = config.getServers().get(name);
    if (target == null) {
      log.warn("no target found for:{}", dst);
      wss.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("ERROR", "cannot found server: " + name, dst.isSecure())));
      return null;
    }
    HostAndPort server = HostAndPort.fromString(target);
    ChannelInitializer<SocketChannel> channelInitializer = new ChannelInitializer<>() {
      @Override
      protected void initChannel(@NonNull SocketChannel ch) {
        ChannelPipeline p = ch.pipeline();
        // 全局限速
        p.addLast(globalTrafficShapingHandler);
        // 限速
        p.addLast(new ChannelTrafficShapingHandler(dst.getWriteBytesPerSecond(), dst.getReadBytesPerSecond()));
        if (config.isEnableDebug()) {
          p.addLast(new LoggingHandler(LogLevel.INFO));
        }
        p.addLast(new ReadTimeoutHandler(dst.getReadTimeout()));
        Counter counter = metric.counter("server_send_bytes", "server", ProxyUtil.formatMetricTag(name));
        p.addLast(new ForwardServerHandler(server, wss, dst.isSecure(), counter));
      }
    };
    Bootstrap bootstrap = new Bootstrap();
    bootstrap.group(bossGroup)
            .channel(NioSocketChannel.class)
            .handler(channelInitializer)
            .option(ChannelOption.AUTO_READ, true)
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, dst.getConnectTimeout() * 1000);
    // 连接远程服务器
    log.info("TRY connect target: {}  ws: {}", server, wss.getId());
    ChannelFuture f = bootstrap.connect(server.getHost(), server.getPort());
    f.addListener((ChannelFutureListener) future -> {
      if (!future.isSuccess()) {
        log.warn("FAIL connect {}  ws: {}", server, wss.getId());
        wss.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("ERROR", "cannot connect: " + server, dst.isSecure())));
      } else {
        log.info("SUCCESS connect {}  ws: {}", server, wss.getId());
        wss.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("CONNECTED", "connected " + server, dst.isSecure())));
      }
    });
    return f.channel();
  }
}
