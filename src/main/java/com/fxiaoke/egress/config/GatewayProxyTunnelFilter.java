package com.fxiaoke.egress.config;

import com.fxiaoke.egress.server.LocalServerManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.cloud.gateway.server.mvc.common.MvcUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.function.ServerRequest;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Slf4j
public class GatewayProxyTunnelFilter {

  /**
   * path 第一级是contextPath，第二级是gateway固定前缀，第三级是应用的名字
   */
  private static final int PATH_PARTS = 3;

  public static Function<ServerRequest, ServerRequest> proxyTunnelTarget() {
    return (request) -> {
      //根据URL判断有没有配置对象
      Collection<Map<String, String>> settings = LocalServerManager.settingsCache().values();
      // 注意此时的path带有contextPath的：/egress-proxy-service/tunnel/fs-bi-industry-foneshare/tester
      String path = request.uri().getRawPath();
      String[] originalParts = StringUtils.tokenizeToStringArray(path, "/");
      if (originalParts.length < PATH_PARTS) {
        log.warn("not found proxy target: {}", path);
        return request;
      }
      Map<String, String> options = settings.stream()
              .filter(m -> originalParts[PATH_PARTS - 1].equals(m.get("name")))
              .findFirst()
              .orElse(null);
      if (MapUtils.isEmpty(options)) {
        log.warn("not found proxy target: {}", path);
        return request;
      }
      String targetUrl = options.getOrDefault("scheme", "http") + "://" + options.get("local");
      URI prefixedUri = stripPrefix(request, originalParts, path);
      ServerRequest.Builder builder = ServerRequest.from(request);
      headers(options.get("headers")).forEach(builder::header);
      ServerRequest modifyRequest = builder.uri(prefixedUri).build();
      try {
        URI url = new URI(targetUrl);
        MvcUtils.setRequestUrl(modifyRequest, url);
      } catch (URISyntaxException e) {
        log.warn("invalid target url: {}", targetUrl);
      }
      return modifyRequest;
    };
  }

  private static Map<String, String> headers(String headers) {
    if (!StringUtils.hasText(headers)) {
      return Map.of();
    }
    Map<String, String> map = new HashMap<>();
    for (String line : headers.split(",")) {
      String[] split = line.split(":", 2);
      if (split.length == 2) {
        map.put(split[0], split[1]);
      }
    }
    return map;
  }

  private static URI stripPrefix(ServerRequest request, String[] originalParts, String path) {
    StringBuilder newPath = new StringBuilder("/");

    for (int i = 0; i < originalParts.length; ++i) {
      if (i >= PATH_PARTS) {
        if (newPath.length() > 1) {
          newPath.append('/');
        }
        newPath.append(originalParts[i]);
      }
    }
    if (newPath.length() > 1 && path.endsWith("/")) {
      newPath.append('/');
    }
    return UriComponentsBuilder.fromUri(request.uri()).replacePath(newPath.toString()).build().toUri();
  }

}
