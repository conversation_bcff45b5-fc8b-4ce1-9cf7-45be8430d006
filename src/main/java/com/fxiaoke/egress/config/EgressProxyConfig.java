package com.fxiaoke.egress.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

@Data
@Configuration
@ConfigurationProperties(prefix = "egress.proxy")
public class EgressProxyConfig {
  /**
   * 代理服务绑定的ip，如果有多个ip地址，可以指定绑定其中1个
   */
  private String bindIp;
  /**
   * 代理服务绑定的端口
   */
  private int bindPort = 9999;
  /**
   * 代理服务的backlog
   */
  private int backlog;
  private int connectTimeoutInSeconds = 10;
  private int readTimeoutInSeconds = 1800;
  private long readBytesPerSeconds = 1024 * 1024L;
  private long writeBytesPerSeconds = 1024 * 1024L;
  /**
   * 是否打印debug日志
   */
  private boolean enableDebug = false;
  /**
   * 是否允许匿名访问
   */
  private boolean enableAnonymousAccess = true;
  /**
   * 是否允许访问任何站点
   */
  private boolean enableAnySite = true;
  /**
   * 是否允许访问内网地址，为了安全考虑，部分场景下，proxy不允许访问内网地址
   */
  private boolean enableLocalSite = true;
  /**
   * 允许访问的app，通过X-Peer-Name来传递，允许这类app访问任何站点
   */
  private Set<String> allowedApps = Set.of();
  /**
   * 允许访问的域名
   */
  private Set<String> allowedSites = Set.of();
  /**
   * basic auth，格式为basic64(username:password)
   */
  private Set<String> basic = Set.of();
}
