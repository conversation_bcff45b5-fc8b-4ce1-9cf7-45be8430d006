package com.fxiaoke.egress.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
public class WebSocketServerConfig {

  @Bean
  public ServletServerContainerFactoryBean createWebSocketContainer(ShareSocketConfig config) {
    // 默认size 8192太小了，很多应用超出大小
    ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
    int bufferSize = (int) config.getSendBufferSize().toBytes();
    container.setMaxTextMessageBufferSize(bufferSize);
    container.setMaxBinaryMessageBufferSize(bufferSize);
    return container;
  }

}
