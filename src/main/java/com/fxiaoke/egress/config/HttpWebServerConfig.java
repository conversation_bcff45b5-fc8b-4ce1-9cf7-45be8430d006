package com.fxiaoke.egress.config;

import org.apache.catalina.connector.Connector;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 同时支持Http和Https服务。这里启动的是http，Spring Boot 默认的配置作为https。
 */
@Configuration
@ConditionalOnProperty(name = "server.http.port")
public class HttpWebServerConfig {

  /**
   * HTTP端口
   */
  @Value("${server.http.port}")
  private int httpPort;

  @Bean
  public ServletWebServerFactory servletContainer() {
    TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();
    tomcat.addAdditionalTomcatConnectors(createStandardConnector());
    return tomcat;
  }

  private Connector createStandardConnector() {
    Connector connector = new Connector("org.apache.coyote.http11.Http11Nio2Protocol");
    connector.setPort(httpPort);
    return connector;
  }

}
