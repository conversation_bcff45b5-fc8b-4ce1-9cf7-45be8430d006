package com.fxiaoke.egress.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Configuration
@ConfigurationProperties(prefix = "egress.websocket")
public class ShareSocketConfig {
  /**
   * websocket的contextPath。<br/>
   * 注意：必须重启才能生效
   */
  private String contextPath = "/FWD";
  /**
   * 允许的跨域请求源，默认允许所有源。<br/>
   * 注意：必须重启才能生效
   */
  private String allowedOrigins = "*";
  /**
   * 全局读的速率限制，单位字节/秒
   */
  private long globalReadBytesPerSecond = 10 * 1024 * 1024L;
  /**
   * 全局写的速率限制，单位字节/秒
   */
  private long globalWriteBytesPerSecond = 10 * 1024 * 1024L;

  /**
   * 发送超时时间，以毫秒为单位
   */
  private int sendTimeLimit = 60_000;

  /**
   * 发送缓存大小，单位字节，避免占用过多内存。Spring Boot 默认是 8K
   */
  private DataSize sendBufferSize = DataSize.ofKilobytes(256);

  /**
   * 启用的server配置，必须在白名单里面的server才允许访问
   */
  private Map<String, String> servers = Map.of();

  /**
   * IP 白名单，只有白名单内的才允许访问，支持正则，注意正则是Java的正则，不是 Spring 正则
   */
  private Set<String> allowedHosts = Set.of("*");

  /**
   * 是否打印debug日志
   */
  private boolean enableDebug = false;

  /**
   * 某些服务本身很不正常，忽略错误日志
   */
  private List<String> ignoreErrors = List.of();

  /**
   * 是否启用心跳机制
   */
  private boolean enableHeartbeat = true;

  /**
   * 心跳间隔时间，单位秒
   */
  private int heartbeatInterval = 30;

  /**
   * 心跳超时时间，单位秒
   */
  private int heartbeatTimeout = 10;

  /**
   * 最大心跳失败次数，超过后断开连接
   */
  private int maxHeartbeatFailures = 3;

}
