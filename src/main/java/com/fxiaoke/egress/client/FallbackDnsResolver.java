package com.fxiaoke.egress.client;

import com.fxiaoke.egress.util.IpUtils;
import io.netty.util.internal.SocketUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.java_websocket.client.DnsResolver;

import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;

/**
 * 默认DNS解析是InetAddress.getByName，在老K8S集群内不稳定
 */
public class FallbackDnsResolver implements DnsResolver {

  private final String[] serverList;

  public FallbackDnsResolver(String tunnelAliases) {
    this.serverList = StringUtils.isBlank(tunnelAliases) ? null : tunnelAliases.split(",");
  }

  @Override
  public InetAddress resolve(URI uri) throws UnknownHostException {
    String host = uri.getHost();
    try {
      return SocketUtils.addressByName(host);
    } catch (UnknownHostException e) {
      if (ArrayUtils.isEmpty(serverList)) {
        throw e;
      }
      return InetAddress.getByName(IpUtils.roundRobin(serverList));
    }
  }
}
