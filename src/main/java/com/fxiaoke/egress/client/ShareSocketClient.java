package com.fxiaoke.egress.client;

import com.fxiaoke.egress.bean.MessageBean;
import com.fxiaoke.egress.bean.ProxyTarget;
import com.fxiaoke.egress.util.ProxyUtil;
import io.micrometer.core.instrument.Counter;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.io.EOFException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 在本地client和websocket服务之间转发
 *
 * <AUTHOR>
 */
@Slf4j
public class ShareSocketClient extends WebSocketClient {
  private final ProxyTarget target;
  private final Counter counter;
  private final List<String> ignoreErrors;

  @Setter
  private ChannelHandlerContext client;

  public ShareSocketClient(URI serverUri, ProxyTarget target, Counter counter, List<String> ignoreErrors) {
    super(serverUri);
    this.target = target;
    this.counter = counter;
    this.ignoreErrors = ignoreErrors;
  }

  @Override
  public void onOpen(ServerHandshake handshake) {
    send(ProxyUtil.binaryMessage("CONNECT", target.toByteArray(), target.isSecure()));
  }

  @Override
  public void onMessage(String message) {
    throw new IllegalStateException("Unexpected text message: " + message);
  }

  @Override
  public void onMessage(ByteBuffer bytes) {
    counter.increment(bytes.remaining());
    MessageBean bean = MessageBean.fromByteBuffer(bytes);
    log.debug("read from binder: {}", bean);
    switch (bean.getAction()) {
      case "CONNECTED":
        log.debug("target: {}, {}, now try read from local", target.getName(), new String(bean.decode(), StandardCharsets.UTF_8));
        client.channel().config().setAutoRead(true);
        break;
      case "CONTINUE":
        byte[] content = bean.decode();
        client.writeAndFlush(Unpooled.wrappedBuffer(content));
        log.debug("<<<<<< binder sent to local {}, {} bytes", target.getName(), content.length);
        break;
      case "DISCONNECT":
        close();
        log.info("disconnect from {}", target.getName());
        break;
      case "ERROR":
        close();
        logError(bean);
        break;
      default:
        log.warn("target: {}, action: {}, body: {}", target.getName(), bean.getAction(), new String(bean.decode(), StandardCharsets.UTF_8));
    }
  }

  private void logError(MessageBean bean) {
    if (ignoreErrors.contains(target.getName())) {
      log.warn("target: {}, error: {}", target.getName(), new String(bean.decode(), StandardCharsets.UTF_8));
    } else {
      log.error("target: {}, error: {}", target.getName(), new String(bean.decode(), StandardCharsets.UTF_8));
    }
  }

  @Override
  public void onClose(int code, String reason, boolean remote) {
    log.info("{}, closed with exitCode: {}, reason: {}, remote: {}", target.getName(), code, reason, remote);
    client.close();
  }

  @Override
  public void close() {
    super.close();
    client.close();
  }

  @Override
  public void onError(Exception ex) {
    if (ex instanceof EOFException || ex.getCause() instanceof EOFException || ignoreErrors.contains(target.getName())) {
      log.warn("target: {}, Error occurred", target, ex);
    } else {
      log.error("target: {}, Error occurred", target, ex);
    }
    client.close();
  }
}
