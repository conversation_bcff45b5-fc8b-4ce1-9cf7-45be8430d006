package com.fxiaoke.egress.client;

import com.fxiaoke.egress.bean.MessageBean;
import com.fxiaoke.egress.bean.ProxyTarget;
import com.fxiaoke.egress.util.ProxyUtil;
import io.micrometer.core.instrument.Counter;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.io.EOFException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 在本地client和websocket服务之间转发
 *
 * <AUTHOR>
 */
@Slf4j
public class ShareSocketClient extends WebSocketClient {
  private final ProxyTarget target;
  private final Counter counter;
  private final List<String> ignoreErrors;

  @Setter
  private ChannelHandlerContext client;

  // 心跳相关
  private final ScheduledExecutorService heartbeatExecutor = Executors.newSingleThreadScheduledExecutor();
  private ScheduledFuture<?> heartbeatTask;
  private volatile long lastPongTime = System.currentTimeMillis();
  private volatile int heartbeatFailureCount = 0;

  // 心跳配置 - 可以通过系统属性配置
  private final int heartbeatInterval = Integer.getInteger("egress.websocket.client.heartbeat.interval", 30);
  private final int heartbeatTimeout = Integer.getInteger("egress.websocket.client.heartbeat.timeout", 10);
  private final int maxHeartbeatFailures = Integer.getInteger("egress.websocket.client.heartbeat.max-failures", 3);

  public ShareSocketClient(URI serverUri, ProxyTarget target, Counter counter, List<String> ignoreErrors) {
    super(serverUri);
    this.target = target;
    this.counter = counter;
    this.ignoreErrors = ignoreErrors;
  }

  @Override
  public void onOpen(ServerHandshake handshake) {
    send(ProxyUtil.binaryMessage("CONNECT", target.toByteArray(), target.isSecure()));

    // 启动心跳机制
    startHeartbeat();
    log.info("target: {}, WebSocket connection opened, heartbeat started", target.getName());
  }

  @Override
  public void onMessage(String message) {
    throw new IllegalStateException("Unexpected text message: " + message);
  }

  @Override
  public void onMessage(ByteBuffer bytes) {
    counter.increment(bytes.remaining());
    MessageBean bean = MessageBean.fromByteBuffer(bytes);
    log.debug("read from binder: {}", bean);
    switch (bean.getAction()) {
      case "CONNECTED":
        log.debug("target: {}, {}, now try read from local", target.getName(), new String(bean.decode(), StandardCharsets.UTF_8));
        client.channel().config().setAutoRead(true);
        break;
      case "CONTINUE":
        byte[] content = bean.decode();
        client.writeAndFlush(Unpooled.wrappedBuffer(content));
        log.debug("<<<<<< binder sent to local {}, {} bytes", target.getName(), content.length);
        break;
      case "DISCONNECT":
        close();
        log.info("disconnect from {}", target.getName());
        break;
      case "ERROR":
        close();
        logError(bean);
        break;
      case "PING":
        // 响应PONG消息
        send(ProxyUtil.binaryMessage("PONG", "pong", target.isSecure()));
        log.debug("target: {}, received PING, sent PONG", target.getName());
        break;
      case "PONG":
        // 更新最后收到PONG的时间
        lastPongTime = System.currentTimeMillis();
        heartbeatFailureCount = 0; // 重置失败计数
        log.debug("target: {}, received PONG", target.getName());
        break;
      default:
        log.warn("target: {}, action: {}, body: {}", target.getName(), bean.getAction(), new String(bean.decode(), StandardCharsets.UTF_8));
    }
  }

  private void logError(MessageBean bean) {
    if (ignoreErrors.contains(target.getName())) {
      log.warn("target: {}, error: {}", target.getName(), new String(bean.decode(), StandardCharsets.UTF_8));
    } else {
      log.error("target: {}, error: {}", target.getName(), new String(bean.decode(), StandardCharsets.UTF_8));
    }
  }

  @Override
  public void onClose(int code, String reason, boolean remote) {
    log.info("{}, closed with exitCode: {}, reason: {}, remote: {}", target.getName(), code, reason, remote);
    stopHeartbeat();
    client.close();
  }

  @Override
  public void close() {
    stopHeartbeat();
    super.close();
    client.close();
  }

  @Override
  public void onError(Exception ex) {
    if (ex instanceof EOFException || ex.getCause() instanceof EOFException || ignoreErrors.contains(target.getName())) {
      log.warn("target: {}, Error occurred", target, ex);
    } else {
      log.error("target: {}, Error occurred", target, ex);
    }
    stopHeartbeat();
    client.close();
  }

  /**
   * 启动心跳机制
   */
  private void startHeartbeat() {
    lastPongTime = System.currentTimeMillis();
    heartbeatFailureCount = 0;

    heartbeatTask = heartbeatExecutor.scheduleWithFixedDelay(() -> {
      try {
        sendHeartbeat();
      } catch (Exception e) {
        log.warn("target: {}, heartbeat error", target.getName(), e);
      }
    }, heartbeatInterval, heartbeatInterval, TimeUnit.SECONDS);

    log.debug("target: {}, heartbeat started, interval: {}s", target.getName(), heartbeatInterval);
  }

  /**
   * 停止心跳机制
   */
  private void stopHeartbeat() {
    if (heartbeatTask != null) {
      heartbeatTask.cancel(false);
      heartbeatTask = null;
    }
    if (!heartbeatExecutor.isShutdown()) {
      heartbeatExecutor.shutdown();
    }
    log.debug("target: {}, heartbeat stopped", target.getName());
  }

  /**
   * 发送心跳PING消息
   */
  private void sendHeartbeat() {
    if (!isOpen()) {
      stopHeartbeat();
      return;
    }

    // 检查上次PONG时间
    long timeSinceLastPong = System.currentTimeMillis() - lastPongTime;
    if (timeSinceLastPong > heartbeatTimeout * 1000L) {
      heartbeatFailureCount++;

      log.warn("target: {}, heartbeat timeout, failures: {}/{}, timeSinceLastPong: {}ms",
          target.getName(), heartbeatFailureCount, maxHeartbeatFailures, timeSinceLastPong);

      if (heartbeatFailureCount >= maxHeartbeatFailures) {
        log.error("target: {}, max heartbeat failures reached, closing connection", target.getName());
        close();
        return;
      }
    }

    // 发送PING消息
    send(ProxyUtil.binaryMessage("PING", "ping", target.isSecure()));
    log.debug("target: {}, sent PING", target.getName());
  }
}
