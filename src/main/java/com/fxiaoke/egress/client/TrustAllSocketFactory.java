package com.fxiaoke.egress.client;

import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import lombok.extern.slf4j.Slf4j;

import javax.net.SocketFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

/**
 * 我们的证书太不稳定，经常过期，一过期就大量报错
 */
@Slf4j
public class TrustAllSocketFactory {

  private static final SocketFactory defaultFactory = init();

  private TrustAllSocketFactory() {
  }

  public static SocketFactory getDefault() {
    return defaultFactory;
  }

  private static SocketFactory init() {
    try {
      SSLContext sslContext = SSLContext.getInstance("TLS");
      sslContext.init(null, InsecureTrustManagerFactory.INSTANCE.getTrustManagers(), new java.security.SecureRandom());
      return sslContext.getSocketFactory();
    } catch (NoSuchAlgorithmException | KeyManagementException e) {
      log.error("cannot init ssl socket factory", e);
      return SSLSocketFactory.getDefault();
    }
  }

}
