package com.fxiaoke.egress.client;

import com.fxiaoke.egress.bean.ProxyBean;
import com.fxiaoke.egress.bean.ProxyTarget;
import com.fxiaoke.egress.metric.EgressMetric;
import com.fxiaoke.egress.util.IpUtils;
import com.fxiaoke.egress.util.ProxyUtil;
import io.micrometer.core.instrument.Counter;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;
import java.util.List;

/**
 * WebSocket发送端，本地启动监听端口接收请求，然后通过WebSocket转发到目标地址
 */
@Slf4j
public class TcpClientFrontendHandler extends ChannelInboundHandlerAdapter {
  private final ProxyBean proxy;
  private final ShareSocketClient socket;
  private final long startTime = System.currentTimeMillis();
  private long lastActiveTime = System.currentTimeMillis();
  private final Counter counter;

  public TcpClientFrontendHandler(ProxyBean proxy, EgressMetric metric, List<String> ignoreErrors) {
    this.proxy = proxy;
    ProxyTarget dst = new ProxyTarget(proxy.target(), proxy.connectTimeoutInSeconds(), proxy.readTimeoutInSeconds(), proxy.readBytesPerSecond(), proxy.writeBytesPerSecond(), proxy.secure());
    String name = ProxyUtil.formatMetricTag(proxy.name());
    this.counter = metric.counter("client_send_bytes", "server", name);
    Counter counter2 = metric.counter("client_recv_bytes", "server", name);
    this.socket = new ShareSocketClient(parseRemote(proxy), dst, counter2, ignoreErrors);
    this.socket.setProxy(parseProxy(proxy.proxy()));
    this.socket.setConnectionLostTimeout(proxy.connectionLostTimeout());
    //我们的证书太不稳定，经常过期，一过期就大量报错，不得不信任All
    this.trustAllSocketFactory(proxy.tunnel());
    //默认DNS解析是InetAddress.getByName，在老K8S集群内不稳定，可能因为UDP timeout失败
    this.fallbackDnsResolver(proxy.tunnelAliases());
  }

  @Override
  public void channelActive(@NonNull ChannelHandlerContext ctx) {
    socket.setClient(ctx);
    log.info("connect to {} {} {}", proxy.name(), proxy.local(), proxy.tunnel());
    socket.connect();
  }

  @Override
  public void channelInactive(@NonNull ChannelHandlerContext ctx) {
    // 本地断开连接后，也断开到websocket的连接
    socket.close();
    log.info("{}, close clientToTunnel socket", proxy.name());
  }

  @Override
  public void channelRead(@NonNull ChannelHandlerContext ctx, @NonNull Object msg) {
    ByteBuf buf = (ByteBuf) msg;
    lastActiveTime = System.currentTimeMillis();
    log.debug(">>>>>> local send to tunnel: {} {}", proxy.target(), msg);
    counter.increment(buf.readableBytes());
    socket.send(ProxyUtil.binaryMessage("CONTINUE", buf, proxy.secure()));
  }

  @Override
  public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
    long now = System.currentTimeMillis();
    long cost = now - startTime;
    long idle = now - lastActiveTime;
    socket.close();
    log.warn("caught exception, duration={}, idle={}, target: {}, ch: {}", Duration.ofMillis(cost),
            Duration.ofMillis(idle), proxy.name(), ctx.channel(), cause);
  }

  private URI parseRemote(ProxyBean proxy) {
    URI uri = null;
    try {
      uri = new URI(proxy.tunnel());
    } catch (URISyntaxException e) {
      log.error("cannot parse uri: {}", proxy.tunnel(), e);
    }
    return uri;
  }

  private void trustAllSocketFactory(String tunnel) {
    //忽略SSL校验
    boolean ignoreSsl = Boolean.parseBoolean(System.getProperty("egress.websocket.ignore.ssl", "true"));
    if (IpUtils.isSslTunnel(tunnel) && ignoreSsl) {
      this.socket.setSocketFactory(TrustAllSocketFactory.getDefault());
    }
  }

  private void fallbackDnsResolver(String tunnelAliases) {
    this.socket.setDnsResolver(new FallbackDnsResolver(tunnelAliases));
  }

  private Proxy parseProxy(String proxyUri) {
    if (proxyUri == null || proxyUri.isEmpty()) {
      return Proxy.NO_PROXY;
    }
    if (!proxyUri.contains("://")) {
      proxyUri = "http://" + proxyUri;
    }
    try {
      URI uri = new URI(proxyUri);
      String scheme = uri.getScheme();
      String host = uri.getHost();
      int port = uri.getPort();

      Proxy.Type proxyType = switch (scheme.toLowerCase()) {
        case "http", "https" -> Proxy.Type.HTTP;
        case "socks" -> Proxy.Type.SOCKS;
        default -> throw new IllegalArgumentException("Unsupported proxy type: " + scheme);
      };

      return new Proxy(proxyType, new InetSocketAddress(host, port));
    } catch (Exception e) {
      log.error("cannot parse proxyUri: {}", proxyUri, e);
      return Proxy.NO_PROXY;
    }
  }
}
