package com.fxiaoke.egress.client;

import com.fxiaoke.egress.bean.ProxyBean;
import com.fxiaoke.egress.server.Server;
import com.fxiaoke.egress.util.SingleExecutorUtils;
import com.google.common.net.HostAndPort;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

@Slf4j
public class ProxyLocalServer implements Server {
  private static final EventLoopGroup bossGroup = new NioEventLoopGroup();
  private static final EventLoopGroup workerGroup = new NioEventLoopGroup();
  private final ProxyBean proxy;
  private final Consumer<SocketChannel> initializer;
  private boolean running = false;
  private Thread thread;
  private Channel channel;

  public ProxyLocalServer(ProxyBean proxy, Consumer<SocketChannel> initializer) {
    this.proxy = proxy;
    this.initializer = initializer;
  }

  /**
   * 关闭所有的server
   */
  public static void shutdownAll() {
    bossGroup.shutdownGracefully().syncUninterruptibly();
    workerGroup.shutdownGracefully().syncUninterruptibly();
  }

  @Override
  public boolean isRunning() {
    return running;
  }

  public void startup() {
    log.info("startup, proxy:{}", proxy);
    proxyTcp();
  }

  private void proxyTcp() {
    // Configure the bootstrap.
    ChannelInitializer<SocketChannel> childHandler = new ChannelInitializer<>() {
      @Override
      protected void initChannel(SocketChannel ch) throws Exception {
        initializer.accept(ch);
      }
    };
    ServerBootstrap bootstrap = new ServerBootstrap();
    bootstrap.group(bossGroup, workerGroup)
            .channel(NioServerSocketChannel.class)
            .childHandler(childHandler)
            .option(ChannelOption.SO_REUSEADDR, true)
            .option(ChannelOption.SO_BACKLOG, 1024)
            .option(ChannelOption.AUTO_READ, true)
            .childOption(ChannelOption.AUTO_READ, false)
            .childOption(ChannelOption.TCP_NODELAY, true);

    Runnable runnable = () -> {
      running = true;
      try {
        HostAndPort bind = HostAndPort.fromString(proxy.local());
        channel = bootstrap.bind(bind.getHost(), bind.getPort()).sync().channel();
        log.info("proxy bind success {}={}", proxy.name(), proxy.local());
        channel.closeFuture().sync();
      } catch (InterruptedException ignored) {
        log.info("get interrupted in proxy client {}={}, will try to close", proxy.name(), proxy.local());
        closeChannel();
        Thread.currentThread().interrupt();
      } catch (Exception ex) {
        notifyError(proxy.name(), proxy.local(), ex);
      } finally {
        log.info("EXIT proxyClient {}={}", proxy.name(), proxy.local());
      }
      running = false;
    };
    thread = new Thread(runnable);
    thread.setName("proxy-" + proxy.name());
    thread.start();
  }

  /**
   * 很多服务依赖这个组件，不能因为一个绑定失败就导致所有服务不可用。
   * 但是如果绑定失败，比如端口重复或者关闭失败，是一件影响很大的事情，通过错误日志发出严重警告。
   */
  private void notifyError(String name, String local, Exception ex) {
    AtomicInteger counter = new AtomicInteger(0);
    SingleExecutorUtils.schedule(() -> {
      log.error("proxy {}={} bind error, please check your egress proxy config or restart service.", name, local, ex);
      if (counter.getAndIncrement() > 1000) {
        //主动终止任务
        throw new RuntimeException("just ignore");
      }
    }, Duration.ofSeconds(1));
  }

  private void closeChannel() {
    if (channel != null) {
      log.info("close channel: {} {} {}", proxy.name(), proxy.local(), channel);
      try {
        channel.close().syncUninterruptibly();
        channel.close().awaitUninterruptibly(5, TimeUnit.SECONDS);
      } catch (Exception e) {
        log.warn("close channel failed: {} {} {}", proxy.name(), proxy.local(), channel, e);
      } finally {
        //强制关闭，上面几种方式都会出现关闭失败的情况
        if (channel.isOpen()) {
          log.warn("channel is still open,will force close it: {} {} {}", proxy.name(), proxy.local(), channel);
          channel.unsafe().closeForcibly();
        }
      }
    }
  }

  public void shutdown() {
    if (thread != null) {
      thread.interrupt();
    }
    sleep();
    //补刀，线程的interrupt不及时，直接强制关闭，否则新绑定出现java.net.BindException
    closeChannel();
  }

  private static void sleep() {
    try {
      TimeUnit.MILLISECONDS.sleep(500);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
  }


}
