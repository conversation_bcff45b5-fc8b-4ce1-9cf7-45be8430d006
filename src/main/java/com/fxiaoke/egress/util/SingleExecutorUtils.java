package com.fxiaoke.egress.util;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 单线程ThreadPoolExecutor异步执行任务，注意任务超出后会直接丢弃，只能处理允许丢弃的任务
 */
@Slf4j
@UtilityClass
public class SingleExecutorUtils {

  private static final ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(1);

  public static void schedule(Runnable runnable, Duration duration) {
    executor.scheduleAtFixedRate(runnable, 0, duration.toMillis(), TimeUnit.MILLISECONDS);
  }

}
