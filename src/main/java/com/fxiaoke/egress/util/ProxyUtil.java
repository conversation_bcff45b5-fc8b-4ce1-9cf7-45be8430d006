package com.fxiaoke.egress.util;

import com.fxiaoke.egress.bean.MessageBean;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelOutboundInvoker;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomStringUtils;

import java.io.Closeable;
import java.io.IOException;
import java.util.regex.Pattern;

/**
 * 内部类，禁止外部使用
 *
 * <AUTHOR>
 */
public final class ProxyUtil {
  private static final Pattern ILLEGAL_CHAR = Pattern.compile("[^a-z0-9_]");

  private ProxyUtil() {
  }

  public static String format(ChannelHandlerContext ctx, String message) {
    return ctx.channel().toString() + ' ' + message;
  }

  @SneakyThrows(IOException.class)
  public static void closeQuietly(Closeable closeable) {
    if (closeable != null) {
      closeable.close();
    }
  }

  public static void closeQuietly(ChannelOutboundInvoker invoker) {
    if (invoker != null) {
      invoker.close();
    }
  }

  public static byte[] binaryMessage(String action, String body, boolean secure) {
    MessageBean bean = new MessageBean();
    bean.setAction(action);
    bean.setToken(randomToken(secure));
    bean.setBody(bean.encode(body));
    return bean.toByteArray();
  }

  public static byte[] binaryMessage(String action, byte[] content, boolean secure) {
    MessageBean bean = new MessageBean();
    bean.setAction(action);
    bean.setToken(randomToken(secure));
    bean.setBody(bean.encode(content));
    return bean.toByteArray();
  }

  private static String randomToken(boolean secure) {
    return secure ? RandomStringUtils.secure().nextAlphanumeric(16) : null;
  }

  public static byte[] binaryMessage(String action, ByteBuf buf, boolean secure) {
    MessageBean bean = new MessageBean();
    bean.setAction(action);
    bean.setToken(randomToken(secure));
    try {
      // 检查ByteBuf是否支持数组访问
      if (buf.hasArray()) {
        byte[] bytes = buf.array();
        int offset = buf.arrayOffset() + buf.readerIndex();
        int length = buf.readableBytes();
        // 更新读索引，标记所有已读数据
        buf.readerIndex(buf.readerIndex() + length);
        bean.setBody(bean.encode(bytes, offset, length));
      } else {
        // 对于直接缓冲区，需要复制数据
        byte[] bytes = new byte[buf.readableBytes()];
        // 这个方法也会更新读索引
        buf.readBytes(bytes);
        bean.setBody(bean.encode(bytes));
      }
    } finally {
      // 这个方法也会更新读索引
      buf.release();
    }
    return bean.toByteArray();
  }

  public static String formatMetricTag(String tag) {
    return ILLEGAL_CHAR.matcher(tag).replaceAll("_");
  }
}
