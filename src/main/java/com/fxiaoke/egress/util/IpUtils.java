package com.fxiaoke.egress.util;

import com.fxiaoke.common.IpUtil;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.server.ServerHttpRequest;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;
import java.util.stream.Stream;

@UtilityClass
public class IpUtils {

  private static final AtomicInteger roundRobinCounter = new AtomicInteger(0);

  /**
   * 获取客户端真实IP地址
   *
   * @param request request
   * @return ip address
   */
  public static String getRemoteIp(ServerHttpRequest request) {
    String ip = request.getHeaders().getFirst("X-Real-Ip");
    if (StringUtils.isBlank(ip) || IpUtil.isInnerIP(ip)) {
      ip = request.getHeaders().getFirst("X-Forwarded-For");
    }
    if (ip != null && ip.length() > 7) {
      Predicate<String> outSiteIp = s -> !IpUtil.isInnerIP(s);
      return Stream.of(ip.split(",")).filter(outSiteIp).findFirst().orElse(ip);
    }
    return request.getRemoteAddress().getHostString();
  }

  public static boolean isSslTunnel(String url) {
    try {
      return url.startsWith("https://") || url.startsWith("wss://");
    } catch (Exception e) {
      return false;
    }
  }

  public static String roundRobin(String[] serverList) {
    int serverCount = serverList.length;
    int currentServerIndex = roundRobinCounter.incrementAndGet() % serverCount;
    return serverList[currentServerIndex].trim();
  }

}
