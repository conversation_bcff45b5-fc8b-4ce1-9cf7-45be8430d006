package com.fxiaoke.egress.handler;

import com.fxiaoke.egress.util.ProxyUtil;
import com.google.common.net.HostAndPort;
import io.micrometer.core.instrument.Counter;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.WebSocketSession;

import java.time.Duration;

/**
 * 在websocket和目标server之间转发消息
 *
 * <AUTHOR>
 */
@Slf4j
public class ForwardServerHandler extends ChannelInboundHandlerAdapter {
  private final WebSocketSession session;
  private final HostAndPort server;
  private final boolean secure;
  private final Counter counter;

  private final long startTime = System.currentTimeMillis();
  private long lastActiveTime = System.currentTimeMillis();

  public ForwardServerHandler(HostAndPort server, WebSocketSession session, boolean secure, Counter counter) {
    this.server = server;
    this.session = session;
    this.secure = secure;
    this.counter = counter;
  }

  @Override
  public void channelInactive(ChannelHandlerContext ctx) throws Exception {
    session.close();
    log.info("{}, close tunnelToServer socket", server);
  }

  @Override
  public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
    log.debug("read from server: id={}, msg={}", msg.hashCode(), msg);
    lastActiveTime = System.currentTimeMillis();
    ByteBuf buf = (ByteBuf) msg;
    counter.increment(buf.readableBytes());
    log.debug("<<<<<< server send to tunnel: {}", msg);
    session.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("CONTINUE", buf, secure)));
  }

  @Override
  public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
    long now = System.currentTimeMillis();
    long cost = now - startTime;
    long idle = now - lastActiveTime;
    log.warn("duration={}, idle={}, server: {}, channel: {}, reason: {}", Duration.ofMillis(cost),
            Duration.ofMillis(idle), server, ctx.channel(), cause.getMessage());
    session.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("ERROR", cause.getMessage(), secure)));
  }
}
