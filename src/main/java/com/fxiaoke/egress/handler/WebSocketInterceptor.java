package com.fxiaoke.egress.handler;

import com.fxiaoke.egress.config.ShareSocketConfig;
import com.fxiaoke.egress.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;
import java.util.Set;

/**
 * WebSocket握手拦截，目前实现基于IP白名单限制
 */
@Slf4j
public class WebSocketInterceptor implements HandshakeInterceptor {

  private final ShareSocketConfig config;

  public WebSocketInterceptor(ShareSocketConfig config) {
    this.config = config;
  }

  @Override
  public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
    Set<String> allowedHosts = config.getAllowedHosts();
    String remoteIp = IpUtils.getRemoteIp(request);
    if (StringUtils.isEmpty(remoteIp)) {
      log.warn("WebSocket ip check failed, can not get remoteIp");
      return false;
    }
    if (CollectionUtils.isEmpty(allowedHosts) || allowedHosts.contains("*")) {
      //正常情况下不允许为空，这里做容错，防止配错业务不可用
      log.debug("WebSocket ip check, remoteIp: {}, allow all, allowedHosts: {}", remoteIp, allowedHosts);
      return true;
    }
    boolean match = allowedHosts.stream().anyMatch(remoteIp::matches);
    log.debug("WebSocket ip check, remoteIp: {}, match: {}, allowedHosts: {}", remoteIp, match, allowedHosts);
    return match;
  }

  @Override
  public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, @Nullable Exception exception) {
    // do nothing is ok
  }
}
