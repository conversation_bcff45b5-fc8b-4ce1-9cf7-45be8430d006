package com.fxiaoke.egress.handler;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Optional;
import java.util.function.Consumer;

import org.checkerframework.checker.nullness.qual.NonNull;

import com.fxiaoke.egress.config.EgressProxyConfig;
import com.fxiaoke.egress.metric.EgressMetric;
import com.fxiaoke.egress.service.AclService;
import com.fxiaoke.egress.util.ProxyUtil;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.traffic.ChannelTrafficShapingHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * 接受http请求并互转双向流，处理connect请求
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpProxyClientHandler extends ChannelInboundHandlerAdapter {
  private static final byte[] ESTABLISHED = "HTTP/1.1 200 Connection Established\r\n\r\n".getBytes();
  private final AclService aclService;
  private final EgressProxyConfig config;
  private final EgressMetric metric;
  private final HttpHeaderDetector header = new HttpHeaderDetector();
  private final long startTime = System.currentTimeMillis();

  private Channel clientChannel;
  private Channel remoteChannel;
  private InetSocketAddress client;
  private InetSocketAddress server;

  public HttpProxyClientHandler(AclService aclService, EgressProxyConfig config, EgressMetric metric) {
    this.aclService = aclService;
    this.config = config;
    this.metric = metric;
  }

  @Override
  public void channelActive(ChannelHandlerContext ctx) {
    clientChannel = ctx.channel();
    client = (InetSocketAddress) Optional.of(clientChannel).map(Channel::remoteAddress).orElse(null);
  }

  @Override
  public void channelRead(ChannelHandlerContext ctx, Object msg) {
    ByteBuf in = (ByteBuf) msg;
    if (header.isComplete()) {
      if (remoteChannel.isActive()) {
        String app = Optional.ofNullable(header.getAppName()).orElse("unknown");
        metric.increment("proxy_app_send_bytes", in.readableBytes(), "app", ProxyUtil.formatMetricTag(app));
        String host = Optional.ofNullable(header.getHost()).orElse("unknown");
        metric.increment("proxy_host_send_bytes", in.readableBytes(), "host", ProxyUtil.formatMetricTag(host));
        remoteChannel.writeAndFlush(msg);
      } else {
        log.warn("remote channel is not active, close client channel");
        flushAndClose(clientChannel);
      }
      return;
    }
    header.digest(in);
    if (!header.isComplete()) {
      in.release();
      return;
    }

    // disable AutoRead until remote connection is ready
    clientChannel.config().setAutoRead(false);

    if (aclService.isDeny(header)) {
      log.warn("deny: {}, app: {}, client: {}", header.getHost(), header.getAppName(), client);
      in.release();
      String response = denyHostText(header.getHost());
      ByteBuf deny = Unpooled.wrappedBuffer(response.getBytes(StandardCharsets.UTF_8));
      ctx.writeAndFlush(deny).addListener(ChannelFutureListener.CLOSE);
      return;
    }

    connectRemoteServer().addListener((ChannelFutureListener) future -> {
      if (future.isSuccess()) {
        server = (InetSocketAddress) Optional.of(remoteChannel).map(Channel::remoteAddress).orElse(null);
        String serverIp = server.getAddress().getHostAddress();
        log.info("app: {}, connected: {}, remote: {}", header.getAppName(), header.getRemote(), serverIp);
        if (server.getAddress().isSiteLocalAddress()) {
          if (config.isEnableLocalSite()) {
            log.warn("allow local site: {}, app: {}, client: {}", header.getHost(), header.getAppName(), client);
          } else {
            log.warn("deny local site: {}, app: {}, client: {}", header.getHost(), header.getAppName(), client);
            in.release();
            String response = denyHostText(server.getAddress().toString());
            ByteBuf deny = Unpooled.wrappedBuffer(response.getBytes(StandardCharsets.UTF_8));
            ctx.writeAndFlush(deny).addListener(ChannelFutureListener.CLOSE);
            flushAndClose(remoteChannel);
            return;
          }
        }
        // connection is ready, enable AutoRead
        clientChannel.config().setAutoRead(true);
        if (header.isHttps()) {
          // if https, respond 200 to create tunnel
          clientChannel.writeAndFlush(Unpooled.wrappedBuffer(ESTABLISHED));
        } else {
          // forward header and remaining bytes
          ByteBuf buf = header.getByteBuf();
          remoteChannel.write(buf);
        }
        remoteChannel.writeAndFlush(in);
      } else {
        log.error("connect failed, remote: {}, app: {}, client: {}", header.getHost(), header.getAppName(), client);
        in.release();
        String head = "HTTP/1.1 502 Bad Gateway\r\nConnection: close\r\n\r\n";
        ctx.writeAndFlush(Unpooled.wrappedBuffer(head.getBytes(StandardCharsets.UTF_8))).addListener(ChannelFutureListener.CLOSE);
      }
    });
  }

  @NonNull
  private String denyHostText(String host) {
    String html = "<html><body><h3>please contact administrator, add <b>" + host + "</b> into allowed sites.</h3></body></html>";
    byte[] body = html.getBytes(StandardCharsets.UTF_8);
    String head = "HTTP/1.1 403 Forbidden\r\n";
    String type = "Content-Type: text/html; charset=UTF-8\r\n";
    String close = "Connection: close\r\n";
    String length = "Content-Length: " + body.length + "\r\n";
    return head + type + close + length + "\r\n" + html;
  }

  private ChannelFuture connectRemoteServer() {
    Consumer<ChannelHandlerContext> initializer = ctx -> {
      ChannelPipeline pipeline = ctx.pipeline();
      pipeline.addLast(new ChannelTrafficShapingHandler(config.getWriteBytesPerSeconds(), config.getReadBytesPerSeconds()));
      pipeline.addLast(new ReadTimeoutHandler(config.getReadTimeoutInSeconds()));
      if (config.isEnableDebug()) {
        pipeline.addLast(new LoggingHandler(LogLevel.INFO));
      }
    };
    Consumer<ByteBuf> counter = buf -> {
      String app = Optional.ofNullable(header.getAppName()).orElse("unknown");
      metric.increment("proxy_app_received_bytes", buf.readableBytes(), "app", ProxyUtil.formatMetricTag(app));
      String host = Optional.ofNullable(header.getHost()).orElse("unknown");
      metric.increment("proxy_host_received_bytes", buf.readableBytes(), "host", ProxyUtil.formatMetricTag(host));
    };
    Bootstrap boot = new Bootstrap();
    boot
            .group(clientChannel.eventLoop())
            .channel(clientChannel.getClass())
            .option(ChannelOption.SO_KEEPALIVE, Boolean.TRUE)
            .option(ChannelOption.TCP_NODELAY, Boolean.TRUE)
            .option(ChannelOption.AUTO_READ, Boolean.TRUE)
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, config.getConnectTimeoutInSeconds() * 1000)
            .handler(new HttpProxyRemoteHandler(clientChannel, initializer, counter));
    ChannelFuture future = boot.connect(header.getHost(), header.getPort());
    remoteChannel = future.channel();
    return future;
  }

  @Override
  public void channelInactive(ChannelHandlerContext ctx) {
    flushAndClose(remoteChannel);
  }

  @Override
  public void exceptionCaught(ChannelHandlerContext ctx, Throwable e) {
    flushAndClose(clientChannel);
    log.error("server: {}, remote: {}, reason: {}", header, server, e.getMessage());
  }

  private void flushAndClose(Channel ch) {
    var cost = Duration.ofMillis(System.currentTimeMillis() - startTime);
    log.info("cost: {}, app: {}, proxy: {} {}, remote: {}", cost, header.getAppName(), header.getMethod(), header.getRemote(), serverAddress());
    if (ch != null && ch.isActive()) {
      ch.writeAndFlush(Unpooled.EMPTY_BUFFER).addListener(ChannelFutureListener.CLOSE);
    }
  }

  private String serverAddress() {
    return Optional.ofNullable(server)
            .map(InetSocketAddress::getAddress)
            .map(InetAddress::getHostAddress)
            .orElse("unknown");
  }

}
