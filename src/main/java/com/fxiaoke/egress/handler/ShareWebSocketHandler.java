package com.fxiaoke.egress.handler;

import com.fxiaoke.egress.bean.MessageBean;
import com.fxiaoke.egress.bean.ProxyTarget;
import com.fxiaoke.egress.config.ShareSocketConfig;
import com.fxiaoke.egress.util.ProxyUtil;
import com.google.common.collect.Maps;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.PongMessage;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.io.EOFException;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.UnaryOperator;

@Slf4j
public class ShareWebSocketHandler extends AbstractWebSocketHandler {
  private final UnaryOperator<WebSocketSession> operator;
  private final BiFunction<ProxyTarget, WebSocketSession, Channel> connector;
  private final ShareSocketConfig config;
  private final Map<String, WebSocketSession> sessions = Maps.newConcurrentMap();
  private final Map<String, Channel> channels = Maps.newConcurrentMap();

  // 心跳相关
  private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(2);
  private final Map<String, ScheduledFuture<?>> heartbeatTasks = Maps.newConcurrentMap();
  private final Map<String, Long> lastPongTime = Maps.newConcurrentMap();
  private final Map<String, Integer> heartbeatFailureCount = Maps.newConcurrentMap();

  public ShareWebSocketHandler(UnaryOperator<WebSocketSession> operator,
      BiFunction<ProxyTarget, WebSocketSession, Channel> connector,
      ShareSocketConfig config) {
    this.operator = operator;
    this.connector = connector;
    this.config = config;
  }

  @Override
  public void afterConnectionEstablished(WebSocketSession session) {
    String id = session.getId();
    sessions.put(id, operator.apply(session));
    log.info("connection established sessionId={}, LocalAddress: {}, RemoteAddress: {}, uri:{}", id,
            session.getLocalAddress(), session.getRemoteAddress(), session.getUri());

    // 启动心跳机制
    if (config.isEnableHeartbeat()) {
      startHeartbeat(session);
    }
  }

  @Override
  @SuppressWarnings("all")
  public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
    String sessionId = session.getId();
        log.info("close sessionId={}, LocalAddress: {}, RemoteAddress: {}, status: {}, uri:{}", sessionId,
        session.getLocalAddress(), session.getRemoteAddress(),
            status, session.getUri());

    // 停止心跳
    stopHeartbeat(sessionId);

    Optional.ofNullable(sessions.remove(sessionId)).ifPresent(ProxyUtil::closeQuietly);
    Optional.ofNullable(channels.remove(sessionId)).ifPresent(ProxyUtil::closeQuietly);
  }

  @Override
  @SuppressWarnings("all")
  public void handleTransportError(WebSocketSession session, Throwable exception) {
    if (exception instanceof EOFException || exception.getCause() instanceof EOFException) {
      log.warn("transport error sessionId={}, LocalAddress: {}, RemoteAddress: {}, uri:{}",
              session.getId(), session.getLocalAddress(), session.getRemoteAddress(), session.getUri(), exception);
    } else {
      log.error("transport error sessionId={}, LocalAddress: {}, RemoteAddress: {}, uri:{}",
              session.getId(), session.getLocalAddress(), session.getRemoteAddress(), session.getUri(), exception);
    }
  }

  @Override
  @SuppressWarnings("all")
  protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
    log.error("Unexpected WebSocket message {}", message);
    throw new IllegalStateException("Unexpected WebSocket message type: " + message);
  }

  @Override
  protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws IOException {
    MessageBean bean = MessageBean.fromByteBuffer(message.getPayload());
    String sessionId = session.getId();
    String action = bean.getAction();
    log.debug("sessionId: {}, action:{}, message: {}", sessionId, action, bean);
    switch (action) {
      case "CONNECT":
        ProxyTarget dst = ProxyTarget.fromByteArray(bean.decode());
        Optional.ofNullable(connector.apply(dst, sessions.get(sessionId))).ifPresent(c -> channels.put(sessionId, c));
        break;
      case "CONTINUE":
        Optional.ofNullable(channels.get(sessionId))
                .ifPresentOrElse(ch -> {
                          ByteBuf buf = Unpooled.wrappedBuffer(bean.decode());
                          ch.writeAndFlush(buf);
                          log.debug(">>>>>> tunnel send to server: {}", buf);
                        },
                        //正常不应该为null，但是确实有找不到channel的情况加日志看下为啥
                        () -> log.error("sessionId: {}, channel is null, LocalAddress: {}, RemoteAddress: {},", sessionId, session.getLocalAddress(), session.getRemoteAddress()));
        break;
      case "DISCONNECT":
        session.close();
        break;
      case "PING":
        // 响应PONG消息
        session.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("PONG", "pong", false)));
        log.debug("sessionId: {}, received PING, sent PONG", sessionId);
        break;
      case "PONG":
        // 更新最后收到PONG的时间
        lastPongTime.put(sessionId, System.currentTimeMillis());
        heartbeatFailureCount.put(sessionId, 0); // 重置失败计数
        log.debug("sessionId: {}, received PONG", sessionId);
        break;
      default:
        session.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("ERROR", "unknown action: " + action, false)));
    }
  }

  @Override
  protected void handlePongMessage(WebSocketSession session, PongMessage message) throws Exception {
    String sessionId = session.getId();
    lastPongTime.put(sessionId, System.currentTimeMillis());
    heartbeatFailureCount.put(sessionId, 0);
    log.debug("sessionId: {}, received WebSocket PONG", sessionId);
  }

  /**
   * 启动心跳机制
   */
  private void startHeartbeat(WebSocketSession session) {
    String sessionId = session.getId();
    lastPongTime.put(sessionId, System.currentTimeMillis());
    heartbeatFailureCount.put(sessionId, 0);

    ScheduledFuture<?> heartbeatTask = heartbeatExecutor.scheduleWithFixedDelay(() -> {
      try {
        sendHeartbeat(session);
      } catch (Exception e) {
        log.warn("sessionId: {}, heartbeat error", sessionId, e);
      }
    }, config.getHeartbeatInterval(), config.getHeartbeatInterval(), TimeUnit.SECONDS);

    heartbeatTasks.put(sessionId, heartbeatTask);
    log.debug("sessionId: {}, heartbeat started, interval: {}s", sessionId, config.getHeartbeatInterval());
  }

  /**
   * 停止心跳机制
   */
  private void stopHeartbeat(String sessionId) {
    ScheduledFuture<?> task = heartbeatTasks.remove(sessionId);
    if (task != null) {
      task.cancel(false);
    }
    lastPongTime.remove(sessionId);
    heartbeatFailureCount.remove(sessionId);
    log.debug("sessionId: {}, heartbeat stopped", sessionId);
  }

  /**
   * 发送心跳PING消息
   */
  private void sendHeartbeat(WebSocketSession session) throws IOException {
    String sessionId = session.getId();
    if (!session.isOpen()) {
      stopHeartbeat(sessionId);
      return;
    }

    // 检查上次PONG时间
    Long lastPong = lastPongTime.get(sessionId);
    if (lastPong != null) {
      long timeSinceLastPong = System.currentTimeMillis() - lastPong;
      if (timeSinceLastPong > config.getHeartbeatTimeout() * 1000L) {
        int failures = heartbeatFailureCount.getOrDefault(sessionId, 0) + 1;
        heartbeatFailureCount.put(sessionId, failures);

        log.warn("sessionId: {}, heartbeat timeout, failures: {}/{}, timeSinceLastPong: {}ms",
                sessionId, failures, config.getMaxHeartbeatFailures(), timeSinceLastPong);

        if (failures >= config.getMaxHeartbeatFailures()) {
          log.error("sessionId: {}, max heartbeat failures reached, closing connection", sessionId);
          session.close();
          return;
        }
      }
    }

    // 发送PING消息
    session.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("PING", "ping", false)));
    log.debug("sessionId: {}, sent PING", sessionId);
  }
}
