package com.fxiaoke.egress.handler;

import com.fxiaoke.egress.bean.MessageBean;
import com.fxiaoke.egress.bean.ProxyTarget;
import com.fxiaoke.egress.util.ProxyUtil;
import com.google.common.collect.Maps;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.io.EOFException;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.UnaryOperator;

@Slf4j
public class ShareWebSocketHandler extends AbstractWebSocketHandler {
  private final UnaryOperator<WebSocketSession> operator;
  private final BiFunction<ProxyTarget, WebSocketSession, Channel> connector;
  private final Map<String, WebSocketSession> sessions = Maps.newConcurrentMap();
  private final Map<String, Channel> channels = Maps.newConcurrentMap();

  public ShareWebSocketHandler(UnaryOperator<WebSocketSession> operator, BiFunction<ProxyTarget, WebSocketSession, Channel> connector) {
    this.operator = operator;
    this.connector = connector;
  }

  @Override
  public void afterConnectionEstablished(WebSocketSession session) {
    String id = session.getId();
    sessions.put(id, operator.apply(session));
    log.info("connection established sessionId={}, LocalAddress: {}, RemoteAddress: {}, uri:{}", id,
            session.getLocalAddress(), session.getRemoteAddress(), session.getUri());
  }

  @Override
  @SuppressWarnings("all")
  public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
    log.info("close sessionId={}, LocalAddress: {}, RemoteAddress: {}, status: {}, uri:{}", session.getId(), session.getLocalAddress(), session.getRemoteAddress(),
            status, session.getUri());
    Optional.ofNullable(sessions.remove(session.getId())).ifPresent(ProxyUtil::closeQuietly);
    Optional.ofNullable(channels.remove(session.getId())).ifPresent(ProxyUtil::closeQuietly);
  }

  @Override
  @SuppressWarnings("all")
  public void handleTransportError(WebSocketSession session, Throwable exception) {
    if (exception instanceof EOFException || exception.getCause() instanceof EOFException) {
      log.warn("transport error sessionId={}, LocalAddress: {}, RemoteAddress: {}, uri:{}",
              session.getId(), session.getLocalAddress(), session.getRemoteAddress(), session.getUri(), exception);
    } else {
      log.error("transport error sessionId={}, LocalAddress: {}, RemoteAddress: {}, uri:{}",
              session.getId(), session.getLocalAddress(), session.getRemoteAddress(), session.getUri(), exception);
    }
  }

  @Override
  @SuppressWarnings("all")
  protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
    log.error("Unexpected WebSocket message {}", message);
    throw new IllegalStateException("Unexpected WebSocket message type: " + message);
  }

  @Override
  protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws IOException {
    MessageBean bean = MessageBean.fromByteBuffer(message.getPayload());
    String sessionId = session.getId();
    String action = bean.getAction();
    log.debug("sessionId: {}, action:{}, message: {}", sessionId, action, bean);
    switch (action) {
      case "CONNECT":
        ProxyTarget dst = ProxyTarget.fromByteArray(bean.decode());
        Optional.ofNullable(connector.apply(dst, sessions.get(sessionId))).ifPresent(c -> channels.put(sessionId, c));
        break;
      case "CONTINUE":
        Optional.ofNullable(channels.get(sessionId))
                .ifPresentOrElse(ch -> {
                          ByteBuf buf = Unpooled.wrappedBuffer(bean.decode());
                          ch.writeAndFlush(buf);
                          log.debug(">>>>>> tunnel send to server: {}", buf);
                        },
                        //正常不应该为null，但是确实有找不到channel的情况加日志看下为啥
                        () -> log.error("sessionId: {}, channel is null, LocalAddress: {}, RemoteAddress: {},", sessionId, session.getLocalAddress(), session.getRemoteAddress()));
        break;
      case "DISCONNECT":
        session.close();
        break;
      default:
        session.sendMessage(new BinaryMessage(ProxyUtil.binaryMessage("ERROR", "unknown action: " + action, false)));
    }
  }
}
