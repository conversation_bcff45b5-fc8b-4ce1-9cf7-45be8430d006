package com.fxiaoke.egress.handler;

import java.net.SocketAddress;
import java.util.Optional;
import java.util.function.Consumer;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

/**
 * 提供http代理服务，支持配置白名单
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpProxyRemoteHandler extends ChannelInboundHandlerAdapter {
  private final Channel clientChannel;
  private final Consumer<ChannelHandlerContext> initializer;
  private final Consumer<ByteBuf> counter;
  private Channel remoteChannel;

  public HttpProxyRemoteHandler(Channel clientChannel, Consumer<ChannelHandlerContext> initializer, Consumer<ByteBuf> counter) {
    this.clientChannel = clientChannel;
    this.initializer = initializer;
    this.counter = counter;
  }

  @Override
  public void channelActive(ChannelHandlerContext ctx) {
    initializer.accept(ctx);
    this.remoteChannel = ctx.channel();
  }

  @Override
  public void channelRead(ChannelHandlerContext ctx, Object msg) {
    // just forward
    if (clientChannel.isActive()) {
      counter.accept((ByteBuf) msg);
      clientChannel.writeAndFlush(msg);
    } else {
      log.warn("client channel is not active, close remote channel");
      flushAndClose(remoteChannel);
    }
  }

  @Override
  public void channelInactive(ChannelHandlerContext ctx) {
    flushAndClose(clientChannel);
  }

  @Override
  public void exceptionCaught(ChannelHandlerContext ctx, Throwable e) {
    SocketAddress remote = Optional.ofNullable(ctx.channel()).filter(Channel::isActive).map(Channel::remoteAddress).orElse(null);
    flushAndClose(remoteChannel);
    log.error("remote: {}, reason: {}", remote, e.getMessage());
  }

  private void flushAndClose(Channel ch) {
    if (ch != null && ch.isActive()) {
      ch.writeAndFlush(Unpooled.EMPTY_BUFFER).addListener(ChannelFutureListener.CLOSE);
    }
  }
}
