package com.fxiaoke.egress.handler;

import com.google.common.base.Splitter;
import com.google.common.net.HostAndPort;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.function.Predicate;

/**
 * 解析http头信息
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@ToString(of = {"method", "host", "port", "https"})
public class HttpHeaderDetector {
  private String method;
  private String host;
  private int port;
  private boolean https;
  private boolean complete;
  private String appName;
  private String basicAuth;
  private ByteBuf byteBuf = Unpooled.buffer();

  private final StringBuilder lineBuf = new StringBuilder();

  public void digest(ByteBuf in) {
    while (in.isReadable()) {
      if (complete) {
        throw new IllegalStateException("read header already completed");
      }
      String line = readLine(in);
      if (line == null) {
        return;
      }
      if (method == null) {
        parseHttpMethod(line);
      }
      if (StringUtils.startsWithIgnoreCase(line, "HOST:")) {
        HostAndPort parsed = parseHost(line.substring(6).trim());
        host = parsed.getHost();
        port = parsed.getPort();
      } else if (StringUtils.startsWithIgnoreCase(line, "X-PEER-NAME:")) {
        appName = line.substring(13).trim();
      } else if (StringUtils.startsWithIgnoreCase(line, "PROXY-AUTHORIZATION: BASIC")) {
        basicAuth = line.substring(26).trim();
      } else if (StringUtils.startsWithIgnoreCase(line, "USER-AGENT:") && appName == null) {
        appName = line.substring(11).trim();
      } else if (line.isEmpty()) {
        removeProxyHeaders();
        complete = true;
        break;
      } else {
        log.debug("skip header: {}", line);
      }
    }
  }

  @SuppressWarnings("UnstableApiUsage")
  private void removeProxyHeaders() {
    if (basicAuth != null) {
      String raw = byteBuf.toString(StandardCharsets.UTF_8);
      StringBuilder sbd = new StringBuilder(byteBuf.capacity());
      Predicate<String> filter = s -> StringUtils.startsWithIgnoreCase(s, "PROXY-AUTHORIZATION") || StringUtils.startsWithIgnoreCase(s, "PROXY-CONNECTION");
      Splitter.on("\r\n").omitEmptyStrings().splitToStream(raw).filter(s -> !filter.test(s)).forEach(s -> sbd.append(s).append("\r\n"));
      sbd.append("\r\n");
      byteBuf = Unpooled.copiedBuffer(sbd.toString(), StandardCharsets.UTF_8);
    }
  }

  private void parseHttpMethod(String line) {
    int idx = line.indexOf(' ');
    if (idx > 0) {
      // the first word is http method name
      method = line.substring(0, idx);
      // method CONNECT means https
      https = method.equalsIgnoreCase("CONNECT");
    }
  }

  private HostAndPort parseHost(String host) {
    int idx = host.indexOf(':');
    if (idx > 0) {
      return HostAndPort.fromParts(host.substring(0, idx), Integer.parseInt(host.substring(idx + 1)));
    } else {
      return HostAndPort.fromParts(host, https ? 443 : 80);
    }
  }

  private String readLine(ByteBuf in) {
    while (in.isReadable()) {
      byte b = in.readByte();
      byteBuf.writeByte(b);
      lineBuf.append((char) b);
      int len = lineBuf.length();
      if (len >= 2 && lineBuf.substring(len - 2).equals("\r\n")) {
        String line = lineBuf.substring(0, len - 2);
        lineBuf.delete(0, len);
        return line;
      }
    }
    return null;
  }

  public String getRemote() {
    if (https) {
      return port == 443 ? "https://" + host : "https://" + host + ":" + port;
    }
    return port == 80 ? "http://" + host : "http://" + host + ":" + port;
  }
}
