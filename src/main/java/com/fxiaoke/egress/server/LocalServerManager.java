package com.fxiaoke.egress.server;

import com.fxiaoke.egress.bean.ProxyBean;
import com.fxiaoke.egress.client.ProxyLocalServer;
import com.fxiaoke.egress.client.TcpClientFrontendHandler;
import com.fxiaoke.egress.config.ShareSocketConfig;
import com.fxiaoke.egress.metric.EgressMetric;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Maps;
import io.netty.channel.ChannelPipeline;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.traffic.ChannelTrafficShapingHandler;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.configuration2.INIConfiguration;
import org.apache.commons.configuration2.SubnodeConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 动态加载配置，启停服务
 */
@Slf4j
@Component
@Lazy(false)
public class LocalServerManager {
  private final Map<String, Server> servers = new HashMap<>();
  private static final Map<String, Map<String, String>> settings = new HashMap<>();
  private final ShareSocketConfig socketConfig;
  private final EgressMetric metric;

  @Value("${egress.proxy.tunnel.config.name:egress-proxy-tunnel}")
  private String name = "egress-proxy-tunnel";

  /**
   * 支持配置的字段。其中description为了统计列表的时候方便看明白
   */
  private static final List<String> keys = List.of("local", "tunnel", "target", "proxy", "scheme", "headers",
          "tunnel-aliases", "connect-timeout", "read-timeout",
          "read-bytes-per-second", "write-bytes-per-second",
          "secure", "description");

  public LocalServerManager(ShareSocketConfig socketConfig, EgressMetric metric) {
    this.socketConfig = socketConfig;
    this.metric = metric;
  }

  @PostConstruct
  public void init() {
    ConfigFactory.getConfig(name, this::reload);
  }

  private void reload(IConfig conf) {
    INIConfiguration ini = new INIConfiguration();
    try (StringReader reader = new StringReader(new String(conf.getContent(), StandardCharsets.UTF_8))) {
      ini.read(reader);
    } catch (Exception e) {
      log.error("cannot load config: {}, ", conf.getName(), e);
      return;
    }
    Map<String, Map<String, String>> configs = new HashMap<>();
    SubnodeConfiguration common = ini.getSection("common");
    //与common配置merge
    ini.getSections().stream().filter(s -> !"common".equals(s)).forEach(name -> {
      Map<String, String> options = parseConfig(ini.getSection(name), common);
      options.put("name", name);
      // 以本地bind的地址作为标识，避免多次绑定出错
      String id = options.get("local");
      configs.put(id, options);
    });

    // 识别配置变更先后需要重启、停止、新启动的服务
    configs.forEach((bind, options) -> {
      Map<String, String> oldOptions = settings.getOrDefault(bind, Map.of());
      if (oldOptions.equals(options)) {
        // 新旧配置一样，不必处理
        log.info("keep {}/{}, not-modified", options.get("name"), bind);
      } else {
        // 先关闭老的服务
        shutdown(bind);
        // 启动新的服务
        startup(bind, options);
      }
    });
    // 如果老服务在新配置中没有，则停止它
    List<String> stopped = settings.keySet().stream().filter(s -> !configs.containsKey(s)).toList();
    stopped.forEach(this::shutdown);
  }

  private void startup(String bind, Map<String, String> options) {
    String name = options.get("name");
    String local = options.get("local");
    String tunnel = options.get("tunnel");
    String target = options.get("target");
    String proxy = options.get("proxy");
    // tunnel对应的IP地址，逗号分割，如果dns解析失败，尝试用此IP地址
    String tunnelAliases = options.get("tunnel-aliases");
    int connectTimeout = MapUtils.getIntValue(options, "connect-timeout", 10_000);
    int readTimeout = MapUtils.getIntValue(options, "read-timeout", 60_000);
    int readBytesPerSecond = MapUtils.getIntValue(options, "read-bytes-per-second", 1024 * 1024);
    int writeBytesPerSecond = MapUtils.getIntValue(options, "write-bytes-per-second", 1024 * 1024);
    int connectionLostTimeout = MapUtils.getIntValue(options, "connection-lost-timeout", 60);
    boolean secure = MapUtils.getBooleanValue(options, "secure", true);
    ProxyBean bean = new ProxyBean(name, local, tunnel, target, proxy, tunnelAliases, connectTimeout, readTimeout,
            readBytesPerSecond, writeBytesPerSecond, connectionLostTimeout, secure);
    Server server = new ProxyLocalServer(bean, ch -> {
      ChannelPipeline p = ch.pipeline();
      if (socketConfig.isEnableDebug()) {
        p.addLast(new LoggingHandler(LogLevel.INFO));
      }
      p.addLast(new ChannelTrafficShapingHandler(socketConfig.getGlobalWriteBytesPerSecond(), socketConfig.getGlobalReadBytesPerSecond()));
      p.addLast(new ReadTimeoutHandler(readTimeout));
      p.addLast(new TcpClientFrontendHandler(bean, metric, socketConfig.getIgnoreErrors()));
    });
    settings.put(bind, options);
    servers.put(bind, server);
    try {
      server.startup();
    } catch (Exception e) {
      log.error("cannot startup {}/{}, ", name, bind, e);
    }
  }

  private Map<String, String> parseConfig(SubnodeConfiguration section, SubnodeConfiguration common) {
    Map<String, String> config = Maps.newHashMap();
    keys.forEach(k -> {
      String v = Optional.ofNullable(section.getString(k)).orElse(common.getString(k, ""));
      if (!v.isEmpty()) {
        config.put(k, v);
      }
    });
    return config;
  }

  @PreDestroy
  public void shutdownAll() {
    for (Map.Entry<String, Server> entry : servers.entrySet()) {
      final Server server = entry.getValue();
      log.info("SHUTDOWN: {}, {}", entry.getKey(), server);
      server.shutdown();
    }
    ProxyLocalServer.shutdownAll();
  }

  private void shutdown(String key) {
    settings.remove(key);
    final Server old = servers.remove(key);
    if (old != null) {
      log.info("shutdown: {}, svr={}", key, old);
      try {
        old.shutdown();
      } catch (Exception e) {
        log.error("cannot shutdown {}, ", key, e);
      }
    }
  }

  public static Map<String, Map<String, String>> settingsCache() {
    return settings;
  }

}
