# 跨云访问
在专属云的情况下，部分业务场景下，需要从A云访问B云提供的服务，如果都改造成走openapi的模式，对于调用方改造太大。
所以需要针对这种场景，需要改造为类似内网隧道的模式，允许在A云里面部署B云服务代理，把跨云的服务变为内网代理模式。

## 实现机制
1. 在A云内启动一个代理程序，侦听本地端口，例如 0.0.0.0:8888
2. 在B云内启动一个转发程序，提供websocket服务，负责编解码和转发数据给B云内的目标服务器。
3. 使用过程中，A云内的程序往代理程序 10.10.10.10:8080 发数据包。代理程序接受到数据包之后，拆分为websocket的消息通过公网发给B云的转发程序。B云的转发程序解码消息后转发给目标服务。

## 支持特性
1. 通讯走基于https的websocket协议wss
2. 每个通讯包都是加密的，而且每次的密钥随机
3. 支持通过http代理走外网首发
4. 支持任意的tcp协议，也包括上层的http协议
5. 服务必须先注册才能使用，不能连接任意地址，必须是受控的服务地址（通过配置文件实现）
6. 流量限速，避免带宽被打满。默认设定了单个连接的限速1M/s,整个跨云通道的限速10M/s。
7. 支持空闲连接的自动断开，避免长时间的连接占用资源。
8. 支持指标上报到监控系统，方便查看流量和连接数等信息。
9. 支持设置是否加密传输，默认加密传输。
10. 基于PB格式传输数据，压缩率高。

## 配置选项
### 本地代理端的配置
修改 **egress-proxy-tunnel** 配置文件，为ini格式的配置文件，增加如下配置项:

|           选项           |   默认值   |           样例            |                 说明                 |
|:----------------------:|:-------:|:-----------------------:|:----------------------------------:|
|         local          |    无    |      0.0.0.0.:8888      |              绑定的本地地址               |
|         tunnel         |    无    |  wss://crm.xyz.com/FWD  |      转发服务的地址，需要先在目标云注册，并配置白名单      |
|         target         |    无    |       configSync        |       目标服务名称，用java的驼峰方式命名规范        |
|         proxy          |    无    | http://192.168.8.8:8080 |              http代理地址              |
|    connect-timeout     |    无    |           10            |     连接超时时间（秒），如果网络距离长可以设置更长时间      |
|      read-timeout      |    无    |           60            | 读超时时间（秒）, 如果是TCP或者外部读数据时间长可以设置更长时间 |
| read-bytes-per-second  | 1048576 |         1024000         |     限制每秒读取的字节数，避免网络超载。默认1MB/s      |
| write-bytes-per-second | 1048576 |         1024000         |     限制每秒发送的字节数，避免网络超载。默认1MB/s      |
|         secure         |  true   |          false          |    是否对数据做加密传输，针对http的连接一定要用加密传输    |


### 远程转发端的配置
在配置文件**spring-cloud-egress-proxy-service**中增加相关配置

|                       选项                       |   默认值    |        样例        |              说明              |
|:----------------------------------------------:|:--------:|:----------------:|:----------------------------:|
|         egress.websocket.context-path          |   /FWD   |     /forward     |     接受远程连接的websocket的路径      |
|        egress.websocket.allowed-origins        |    *     |        *         |            限制访问来源            |
| egress.websocket.global-read-bytes-per-second  | 10485760 |        10        |  限制每秒读取的字节数，避免网络超载。默认10MB/s  |
| egress.websocket.global-write-bytes-per-second | 10485760 |        60        |  限制每秒发送的字节数，避免网络超载。默认10MB/s  |
|        egress.websocket.send-time-limit        |  60000   |      10000       |        异步发送消息超时时间（毫秒）        |
|       egress.websocket.send-buffer-size        | 10485760 |     1024000      |      异步发送消息缓冲区大小。默认10MB      |
|         egress.websocket.enable-debug          |   true   |      false       | 是否启用debug模式，启用后会打印每次接受的数据包信息 |
|      egress.websocket.servers.configSync       |    无     | 10.10.10.10:8080 |     接受的服务名的白名单，所有服务必须先注册     |

## 接入流程
- 第一步：发审批给 @黎贵昂，备注应用信息及需要访问的云环境和目标服务地址。这些地址会添加到代理白名单列表中。模板：
  ```
  #应用跨云访问访问申请(代理模式)#
  服务名：configSync
  云环境： XXX专属云
  目标服务： 10.10.10.10:8080
  备注：定期同步纷享云管理界面修改的配置信息
  ```
  其中服务名**config-sync**是会在两个配置文件中都要使用，遵循JAVA变量命名规则，这个会放到sping的配置文件中。
- 第二步：修改目标专属云配置文件 **spring-cloud-egress-proxy-service**，新增如下配置
  ```ini
  egress.websocket.servers.configSync=10.10.10.10:8080
  ```
- 第三步：修改纷享云配置文件 **egress-proxy-tunnel**，新增如下配置
  ```ini
  [configSync]
  local = 0.0.0.0:8888
  target = configSync
  connect-timeout = 10
  read-timeout = 60
  read-bytes-per-second = 1048576
  write-bytes-per-second = 1048576
  secure = true
  ```
- 第四步：修改纷享云发布系统中**egress-proxy-service**模块中暴露的端口，把上述配置的8888端口能够暴露出来，这样其他服务就能访问了。
